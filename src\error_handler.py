"""
错误处理和日志管理模块
提供统一的错误处理、日志记录和用户通知功能
"""

# ============================================================================
# 标准库导入
# ============================================================================
import logging
import sys
import traceback
from functools import wraps
from typing import Optional, Callable, Any

# ============================================================================
# 第三方库导入
# ============================================================================
from PyQt5.QtWidgets import QMessageBox, QWidget


class ErrorHandler:
    """错误处理器类"""
    
    def __init__(self, logger_name: str = __name__):
        self.logger = logging.getLogger(logger_name)
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志记录器"""
        if not self.logger.handlers:
            # 创建文件处理器
            file_handler = logging.FileHandler('pystation.log', encoding='utf-8')
            file_handler.setLevel(logging.DEBUG)
            
            # 创建控制台处理器
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(logging.INFO)
            
            # 创建格式器
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)
            
            # 添加处理器
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)
            self.logger.setLevel(logging.DEBUG)
    
    def handle_exception(self, exc_type, exc_value, exc_traceback, 
                        parent: Optional[QWidget] = None, 
                        user_message: Optional[str] = None):
        """处理异常"""
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        # 记录详细错误信息
        error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        self.logger.critical(f"未捕获的异常: {error_msg}")
        
        # 显示用户友好的错误消息
        if user_message is None:
            user_message = f"程序发生错误: {str(exc_value)}"
        
        if parent:
            QMessageBox.critical(parent, "程序错误", user_message)
        else:
            QMessageBox.critical(None, "程序错误", user_message)
    
    def log_and_show_error(self, error: Exception, 
                          parent: Optional[QWidget] = None,
                          user_message: Optional[str] = None,
                          log_level: int = logging.ERROR):
        """记录错误并显示给用户"""
        # 记录错误
        self.logger.log(log_level, f"错误: {str(error)}", exc_info=True)
        
        # 显示给用户
        if user_message is None:
            user_message = str(error)
        
        if parent:
            QMessageBox.warning(parent, "错误", user_message)
        else:
            QMessageBox.warning(None, "错误", user_message)
    
    def log_and_show_warning(self, message: str, 
                           parent: Optional[QWidget] = None,
                           title: str = "警告"):
        """记录警告并显示给用户"""
        self.logger.warning(message)
        
        if parent:
            QMessageBox.warning(parent, title, message)
        else:
            QMessageBox.warning(None, title, message)
    
    def log_and_show_info(self, message: str, 
                         parent: Optional[QWidget] = None,
                         title: str = "信息"):
        """记录信息并显示给用户"""
        self.logger.info(message)
        
        if parent:
            QMessageBox.information(parent, title, message)
        else:
            QMessageBox.information(None, title, message)


def exception_handler(error_handler: ErrorHandler, 
                     user_message: Optional[str] = None,
                     reraise: bool = False):
    """异常处理装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # 获取parent widget（如果是方法调用）
                parent = None
                if args and hasattr(args[0], '__class__') and hasattr(args[0], 'parent'):
                    try:
                        parent = args[0].parent() if callable(args[0].parent) else args[0].parent
                    except:
                        parent = None
                
                error_handler.log_and_show_error(e, parent, user_message)
                
                if reraise:
                    raise
                return None
        return wrapper
    return decorator


def safe_execute(func: Callable, 
                error_handler: ErrorHandler,
                default_return: Any = None,
                user_message: Optional[str] = None,
                parent: Optional[QWidget] = None) -> Any:
    """安全执行函数"""
    try:
        return func()
    except Exception as e:
        error_handler.log_and_show_error(e, parent, user_message)
        return default_return


class ValidationError(Exception):
    """验证错误"""
    pass


class DataProcessingError(Exception):
    """数据处理错误"""
    pass


class UIError(Exception):
    """UI相关错误"""
    pass


class ConfigurationError(Exception):
    """配置错误"""
    pass


def validate_not_none(value: Any, name: str) -> Any:
    """验证值不为None"""
    if value is None:
        raise ValidationError(f"{name} 不能为空")
    return value


def validate_not_empty(value: str, name: str) -> str:
    """验证字符串不为空"""
    if not value or not value.strip():
        raise ValidationError(f"{name} 不能为空")
    return value.strip()


def validate_file_exists(file_path: str) -> str:
    """验证文件存在"""
    import os
    if not os.path.exists(file_path):
        raise ValidationError(f"文件不存在: {file_path}")
    return file_path


def validate_list_not_empty(lst: list, name: str) -> list:
    """验证列表不为空"""
    if not lst:
        raise ValidationError(f"{name} 列表不能为空")
    return lst


# 全局错误处理器实例
global_error_handler = ErrorHandler("PyStation")


def setup_global_exception_handler():
    """设置全局异常处理器"""
    def handle_exception(exc_type, exc_value, exc_traceback):
        global_error_handler.handle_exception(exc_type, exc_value, exc_traceback)
    
    sys.excepthook = handle_exception
