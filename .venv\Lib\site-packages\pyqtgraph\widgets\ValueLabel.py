from time import perf_counter

from .. import functions as fn
from ..Qt import QtWidgets

__all__ = ['ValueLabel']

class ValueLabel(QtWidgets.QLabel):
    """
    QLabel specifically for displaying numerical values.
    Extends QLabel adding some extra functionality:

      - displaying units with si prefix
      - built-in exponential averaging
    """
    
    def __init__(self, parent=None, suffix='', siPrefix=False, averageTime=0, formatStr=None):
        """
        ==============      ==================================================================================
        **Arguments:**
        suffix              (str or None) The suffix to place after the value
        siPrefix            (bool) Whether to add an SI prefix to the units and display a scaled value
        averageTime         (float) The length of time in seconds to average values. If this value
                            is 0, then no averaging is performed. As this value increases
                            the display value will appear to change more slowly and smoothly.
        formatStr           (str) Optionally, provide a format string to use when displaying text. The text
                            will be generated by calling formatStr.format(value=, avgValue=, suffix=)
                            (see Python documentation on str.format)
                            This option is not compatible with siPrefix
        ==============      ==================================================================================
        """
        QtWidgets.QLabel.__init__(self, parent)
        self.values = []
        self.averageTime = averageTime ## no averaging by default
        self.suffix = suffix
        self.siPrefix = siPrefix
        if formatStr is None:
            formatStr = '{avgValue:0.2g} {suffix}'
        self.formatStr = formatStr
    
    def setValue(self, value):
        now = perf_counter()
        self.values.append((now, value))
        cutoff = now - self.averageTime
        while len(self.values) > 0 and self.values[0][0] < cutoff:
            self.values.pop(0)
        self.update()
        
    def setFormatStr(self, text):
        self.formatStr = text
        self.update()
        
    def setAverageTime(self, t):
        self.averageTime = t
        
    def averageValue(self):
        return sum(v[1] for v in self.values) / float(len(self.values))
        
        
    def paintEvent(self, ev):
        self.setText(self.generateText())
        return super().paintEvent(ev)
        
    def generateText(self):
        if len(self.values) == 0:
            return ''
        avg = self.averageValue()
        val = self.values[-1][1]
        if self.siPrefix:
            return fn.siFormat(avg, suffix=self.suffix)
        else:
            return self.formatStr.format(value=val, avgValue=avg, suffix=self.suffix)
            
