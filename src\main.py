
"""
主窗口模块 - PyStation 数据分析工具
提供产品数据分析、SPC统计、帕累托图等功能

优化版本：
- 改进了代码结构和组织
- 优化了性能和内存管理
- 增强了错误处理
- 减少了代码重复
"""

# ============================================================================
# 标准库导入
# ============================================================================
import os
import sys
import time
import shutil
from functools import partial
from typing import List, Optional, Dict, Any, Tuple
from contextlib import contextmanager

# ============================================================================
# 第三方库导入
# ============================================================================
from PyQt5.QtWidgets import (
    QApplication, QMessageBox, QProgressDialog, QVBoxLayout,
    QDialog, QPushButton, QLabel
)
from PyQt5 import QtWidgets, QtGui, QtCore
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QStandardItemModel, QStandardItem

import pyqtgraph as pg
import matplotlib.cm as cm
import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import norm, probplot, anderson
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.backends.backend_qt5agg import NavigationToolbar2QT as NavigationToolbar
import qdarkstyle
from qdarkstyle.light.palette import LightPalette
from qdarkstyle.dark.palette import DarkPalette

# ============================================================================
# 本地模块导入
# ============================================================================
from Ui_main import Ui_MainWindow
from login_window import LoginWindow
from product_path_dialog import ProductPathDialog
from data_utils import process_tmp_files, read_tmp, point_spc, fig_output, report, save_data
from pareto_window import DataHandler, ParetoFigure, ParetoChartWindow, show_error
from spc_table_window import show_spc_table, SpcTableWindow
from error_handler import (
    ErrorHandler, exception_handler, safe_execute, global_error_handler,
    ValidationError, DataProcessingError, UIError, ConfigurationError,
    validate_not_none, validate_not_empty, validate_list_not_empty,
    validate_file_exists, setup_global_exception_handler
)
from config_manager import ConfigManager, ConfigType, ProductConfig, AppSettings


# 设置全局异常处理
setup_global_exception_handler()

# 创建错误处理器
error_handler = ErrorHandler(__name__)
logger = error_handler.logger


class StyleConfig:
    """样式配置类 - 优化版本

    提供项目路径和样式相关的配置功能。
    使用缓存机制提高性能。
    """

    _project_root: Optional[str] = None

    @classmethod
    def get_project_root(cls) -> str:
        """获取项目根目录（带缓存）"""
        if cls._project_root is None:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            cls._project_root = os.path.dirname(current_dir)
        return cls._project_root

    @classmethod
    def get_report_dir(cls, product: str, timestamp: str) -> str:
        """获取报告目录路径"""
        return os.path.join(cls.get_project_root(), "report", f"{product}-{timestamp}")

    @classmethod
    def clear_cache(cls) -> None:
        """清空缓存"""
        cls._project_root = None


class DataManager:
    """数据管理类 - 使用配置管理器的改进版本

    负责管理产品数据、文件列表和数据缓存。
    使用ConfigManager进行配置管理，提供更好的错误处理和验证。

    Attributes:
        config_manager: 配置管理器实例
        last_oktmp_list: 最后一次搜索的有效文件列表
        last_df_tmp: 最后一次读取的数据框
    """

    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """初始化数据管理器

        Args:
            config_manager: 配置管理器实例，如果为None则创建新实例
        """
        if config_manager is None:
            # 创建新的ConfigManager实例以确保使用当前工作目录
            config_manager = ConfigManager()

        self.config_manager = config_manager
        self.last_oktmp_list: List[str] = []
        self.last_df_tmp: Optional[Any] = None

    def load_products(self, force_reload: bool = False) -> List[ProductConfig]:
        """加载产品列表

        Args:
            force_reload: 是否强制重新加载，忽略缓存

        Returns:
            产品配置对象列表

        Raises:
            ConfigurationError: 当配置加载失败时
        """
        try:
            if force_reload:
                self.config_manager.clear_cache()
            products = self.config_manager.get_products()
            logger.info(f"成功加载 {len(products)} 个产品配置")
            return products
        except Exception as e:
            logger.error(f"加载产品配置失败: {e}")
            error_handler.log_and_show_error(e, None, "加载产品配置失败")
            return []

    def get_product_path(self, product_name: str) -> str:
        """根据产品名称获取路径

        Args:
            product_name: 产品名称

        Returns:
            产品路径

        Raises:
            ValidationError: 当产品名称无效或未找到产品时
            ConfigurationError: 当产品路径配置无效时
        """
        validate_not_empty(product_name, "产品名称")

        # 清空缓存以确保读取最新配置
        self.config_manager.clear_cache()
        products = self.load_products()

        for product in products:
            if product.name == product_name:
                if not product.path:
                    raise ConfigurationError(f"产品 {product_name} 的路径配置为空")

                # 验证路径是否存在
                if not os.path.exists(product.path):
                    raise ConfigurationError(f"产品 {product_name} 的路径不存在: {product.path}")

                return product.path

        raise ValidationError(f"未找到产品: {product_name}")

    def get_product_by_name(self, product_name: str) -> ProductConfig:
        """根据名称获取产品配置

        Args:
            product_name: 产品名称

        Returns:
            产品配置对象

        Raises:
            ValidationError: 当未找到产品时
        """
        validate_not_empty(product_name, "产品名称")

        products = self.load_products()

        for product in products:
            if product.name == product_name:
                return product

        raise ValidationError(f"未找到产品: {product_name}")

    def clear_cache(self) -> None:
        """清空数据缓存"""
        self.last_oktmp_list = []
        self.last_df_tmp = None
        logger.info("数据缓存已清空")


class MainWindow(QtWidgets.QMainWindow):
    """主窗口类 - 负责UI交互和业务逻辑协调

    优化版本：
    - 改进了初始化流程
    - 增加了资源管理
    - 优化了UI组件管理
    """

    def __init__(self):
        super().__init__()
        self.ui = Ui_MainWindow()
        self.ui.setupUi(self)

        # 初始化数据管理器
        self.data_manager = DataManager()

        # 初始化UI组件
        self._init_ui_components()
        self._bind_signals()

        # 设置定时器用于延迟加载
        self._setup_delayed_initialization()

        # 显示窗口
        self.showMaximized()

        logger.info("主窗口初始化完成")

    def _init_ui_components(self):
        """初始化UI组件"""
        # matplotlib 图形显示组件
        self.canvas_layout = QVBoxLayout(self.ui.widget_plot)
        self.canvas: Optional[FigureCanvas] = None
        self.toolbar: Optional[NavigationToolbar] = None

        # 其他UI状态
        self.text_item: Optional[pg.TextItem] = None
        self.pareto_window: Optional[ParetoChartWindow] = None
        self.spc_window: Optional[Any] = None

        # 绘图组件缓存
        self.plot_widget: Optional[pg.PlotWidget] = None

        # 性能监控
        self._performance_stats = {
            'plot_count': 0,
            'last_plot_time': 0,
            'memory_usage': 0
        }

    def _setup_delayed_initialization(self):
        """设置延迟初始化"""
        # 使用定时器延迟加载产品列表，避免阻塞UI
        self.init_timer = QTimer()
        self.init_timer.setSingleShot(True)
        self.init_timer.timeout.connect(self.load_product_names)
        self.init_timer.start(100)  # 100ms后加载

    def closeEvent(self, event):
        """窗口关闭事件处理 - 确保资源清理"""
        try:
            # 清理matplotlib资源
            self._cleanup_matplotlib_canvas()

            # 清理子窗口
            if self.pareto_window:
                self.pareto_window.close()
            if self.spc_window:
                self.spc_window.close()

            # 清理数据缓存
            self.data_manager.clear_cache()

            # 清理绘图组件
            if self.plot_widget:
                self.plot_widget.clear()

            logger.info("主窗口资源清理完成")

        except Exception as e:
            logger.error(f"窗口关闭时清理资源失败: {e}")
        finally:
            event.accept()



    def _bind_signals(self):
        """绑定UI信号到槽函数"""
        # 菜单动作
        self.ui.actionProduct_path.triggered.connect(self.product_path_dialog_clicked)
        self.ui.actionExport_report.triggered.connect(self.on_export_report_clicked)
        self.ui.actionProduct_Pareto.triggered.connect(self.on_product_pareto_clicked)
        self.ui.actionProduct_PPK.triggered.connect(self.on_spc_table_clicked)

        # 按钮点击
        self.ui.pushButton_serach.clicked.connect(self.on_search_clicked)
        self.ui.pushButton_analysis.clicked.connect(self.on_analysis_clicked)

        # 列表选择变化
        self.ui.listWidget_product.itemSelectionChanged.connect(self.on_product_selection_changed)
        self.ui.listWidget_sn.itemSelectionChanged.connect(self.on_sn_selection_changed)


    @contextmanager
    def _plot_context(self):
        """绘图上下文管理器，确保资源正确释放"""
        try:
            yield
        except Exception as e:
            logger.error(f"绘图过程中发生错误: {e}")
            raise
        finally:
            # 强制垃圾回收以释放内存
            import gc
            gc.collect()

    def init_plot(self, df: Optional[Any] = None, points: Optional[List[str]] = None) -> None:
        """初始化绘图显示 - 优化版本

        根据提供的数据框和测量点列表创建交互式绘图。
        包括数据点、公差线、中心线的绘制，以及直方图和QQ图的统计分析。

        Args:
            df: 包含测量数据的pandas DataFrame，必须包含以下列：
                - point: 测量点名称
                - snplus: SN序号
                - deviation: 偏差值
                - up_tol: 上公差
                - low_tol: 下公差
                - sn: 序列号
            points: 要绘制的测量点名称列表

        Returns:
            None

        Raises:
            ValidationError: 当输入参数无效时
            DataProcessingError: 当数据处理失败时
            UIError: 当UI更新失败时

        Example:
            >>> self.init_plot(df=measurement_data, points=['Point1', 'Point2'])
        """
        # 输入验证
        if df is None or not points:
            logger.warning("绘图数据无效")
            return

        # 限制最大绘制点数以提高性能
        max_points = 20
        if len(points) > max_points:
            logger.warning(f"测量点数量过多({len(points)})，仅绘制前{max_points}个")
            points = points[:max_points]

        with self._plot_context():
            try:
                # 获取绘图组件
                self.plot_widget = self.ui.PlotWidget
                self._clear_plot_widget()
                self._setup_plot_widget()

                # 预处理数据以提高性能
                filtered_data = self._preprocess_plot_data(df, points)
                if not filtered_data:
                    logger.warning("没有有效的绘图数据")
                    return

                # 设置颜色映射
                cmap = cm.get_cmap('hsv', len(points))

                # 绘制数据
                self._plot_data_points(filtered_data, cmap)

                # 绘制第一个点的直方图和QQ图
                first_point_data = next((data for data in filtered_data if data is not None), None)
                if first_point_data is not None:
                    self.plot_histogram_and_qq(
                        x=first_point_data['deviation'],
                        lower_tol=first_point_data['low_tol'],
                        upper_tol=first_point_data['up_tol']
                    )

                logger.info(f"成功绘制 {len(points)} 个测量点")

            except Exception as e:
                logger.error(f"绘图失败: {e}")
                QMessageBox.warning(self, '错误', f'绘图失败: {e}')

    def _clear_plot_widget(self):
        """清空绘图组件"""
        self.plot_widget.clear()

    def _setup_plot_widget(self):
        """设置绘图组件"""
        self.plot_widget.addLegend()
        self.plot_widget.setBackground('w')
        self.plot_widget.setLabel('bottom', 'snplus')
        self.plot_widget.setLabel('left', 'deviation')

    def _preprocess_plot_data(self, df, points) -> List[Optional[Any]]:
        """预处理绘图数据以提高性能 - 优化版本

        Args:
            df: 原始数据框
            points: 测量点列表

        Returns:
            过滤后的数据列表
        """
        filtered_data = []

        # 使用向量化操作提高性能
        for point in points:
            try:
                # 使用query方法可能更快
                df_filter = df.query(f'point == "{point}"').sort_values(
                    by=['snplus'], ignore_index=True
                )

                if not df_filter.empty:
                    # 限制数据点数量以提高性能
                    max_data_points = 1000
                    if len(df_filter) > max_data_points:
                        # 采样数据以保持性能
                        step = len(df_filter) // max_data_points
                        df_filter = df_filter.iloc[::step].reset_index(drop=True)
                        logger.info(f"测量点 {point} 数据过多，已采样至 {len(df_filter)} 个点")

                    filtered_data.append(df_filter)
                else:
                    filtered_data.append(None)
                    logger.warning(f"测量点 {point} 没有数据")

            except Exception as e:
                logger.error(f"处理测量点 {point} 数据时出错: {e}")
                filtered_data.append(None)

        return filtered_data

    def _plot_data_points(self, filtered_data: List[Optional[Any]], cmap) -> None:
        """绘制数据点 - 优化版本

        Args:
            filtered_data: 过滤后的数据列表
            cmap: 颜色映射
        """
        first_valid_data = None

        for idx, df_filter in enumerate(filtered_data):
            if df_filter is None:
                continue

            # 设置坐标轴（只在第一个有效数据时设置）
            if first_valid_data is None:
                first_valid_data = df_filter
                self._setup_axis_ticks(df_filter)
                self._plot_tolerance_lines(df_filter)

            # 确定颜色
            line_color = self._get_line_color(idx, cmap)

            # 绘制数据线
            point_name = df_filter['point'].iloc[0] if not df_filter.empty else f"Point_{idx}"

            # 优化绘图性能：减少符号大小和线宽
            symbol_size = 6 if len(df_filter) > 100 else 8
            line_width = 1.5 if len(df_filter) > 100 else 2

            plot_data_item = self.plot_widget.plot(
                df_filter['snplus'], df_filter['deviation'],
                pen=pg.mkPen(line_color, width=line_width),
                symbol='o', symbolSize=symbol_size, symbolBrush=line_color,
                name=point_name
            )

            # 连接点击信号，使用partial避免闭包问题
            plot_data_item.sigPointsClicked.connect(
                partial(self.on_point_clicked, plot_data_item=plot_data_item, df_filter=df_filter)
            )

    def _setup_axis_ticks(self, df_filter):
        """设置坐标轴刻度"""
        sn_quantity = df_filter['sn'].nunique()
        self.plot_widget.getAxis('bottom').setTicks([[(i, str(i)) for i in range(sn_quantity + 1)]])
        self.plot_widget.getAxis('bottom').setTickFont(QtGui.QFont('Arial', 6))
        self.plot_widget.getAxis('left').setTickFont(QtGui.QFont('Arial', 6))

    def _plot_tolerance_lines(self, df_filter):
        """绘制公差线和中心线"""
        # 上公差线
        self.plot_widget.plot(
            df_filter['snplus'], df_filter['up_tol'],
            pen=pg.mkPen('red', width=2, style=QtCore.Qt.DashLine),
            name='上公差线'
        )
        # 下公差线
        self.plot_widget.plot(
            df_filter['snplus'], df_filter['low_tol'],
            pen=pg.mkPen('red', width=2, style=QtCore.Qt.DashLine),
            name='下公差线'
        )
        # 中心线
        center_line = (df_filter['up_tol'] + df_filter['low_tol']) / 2
        self.plot_widget.plot(
            df_filter['snplus'], center_line,
            pen=pg.mkPen('green', width=2),
            name='中心线'
        )

    def _get_line_color(self, idx, cmap):
        """获取线条颜色"""
        if idx == 0:
            return 'b'
        else:
            color = cmap(idx - 1)
            return pg.mkColor(
                int(color[0] * 255), int(color[1] * 255), int(color[2] * 255)
            )


    def on_point_clicked(self, plot, points, plot_data_item=None, df_filter=None):
        """处理数据点点击事件 - 优化版本

        Args:
            plot: 绘图对象
            points: 点击的点列表
            plot_data_item: 绘图数据项
            df_filter: 对应的数据框
        """
        try:
            # 移除旧的标签
            self._remove_existing_text_item()

            # 验证数据
            if df_filter is None or df_filter.empty:
                logger.warning("没有找到对应的绘图数据")
                return

            # 处理点击的点（只处理第一个点以提高性能）
            if not points:
                return

            point = points[0]
            x, y = point.pos()

            # 使用向量化操作查找最接近的点
            distances = np.abs(df_filter['snplus'] - x)
            closest_idx = distances.idxmin()

            if closest_idx is not None:
                row_data = df_filter.loc[closest_idx]

                # 获取各个字段的值，使用get方法避免KeyError
                info_data = {
                    'y': y,
                    'up_tol': row_data.get('up_tol', 'Unknown'),
                    'low_tol': row_data.get('low_tol', 'Unknown'),
                    'point': row_data.get('point', 'Unknown'),
                    'x': x,
                    'sn': row_data.get('sn', 'Unknown')
                }

                # 创建信息文本
                info_text = self._format_point_info(info_data)
                self.text_item = pg.TextItem(
                    info_text,
                    anchor=(0, 1),
                    border='w',
                    fill='blue'
                )

                self.plot_widget.addItem(self.text_item)
                self.text_item.setPos(x, y)

        except Exception as e:
            logger.error(f"处理点击事件失败: {e}")

    def _format_point_info(self, info_data: Dict[str, Any]) -> str:
        """格式化点信息文本

        Args:
            info_data: 包含点信息的字典

        Returns:
            格式化后的信息文本
        """
        return (
                f"Y: {info_data['y']:.2f}\n"
                f"Up_tol: {info_data['up_tol']}\n"
                f"Low_tol: {info_data['low_tol']}\n"
                f"Point: {info_data['point']}\n"
                f"X: {info_data['x']:.2f}\n"
                f"SN: {info_data['sn']}")

    def _remove_existing_text_item(self):
        """移除现有的文本项"""
        if self.text_item is not None:
            self.plot_widget.removeItem(self.text_item)
            self.text_item = None


    def plot_histogram_and_qq(self, x, lower_tol, upper_tol):
        """绘制直方图和QQ图 - 优化版本

        Args:
            x: 数据数组
            lower_tol: 下公差
            upper_tol: 上公差
        """
        try:
            # 数据验证
            if len(x) == 0:
                logger.warning("数据为空，无法绘制统计图形")
                return

            # 清理之前的matplotlib图形以防止内存泄漏
            self._cleanup_matplotlib_canvas()

            # 计算统计数据
            stats = self._calculate_statistics(x, lower_tol, upper_tol)

            # 创建图形，使用更合适的尺寸
            fig, axes = plt.subplots(1, 2, figsize=(12, 5),
                                   gridspec_kw={'wspace': 0.3, 'hspace': 0.3})

            # 绘制直方图
            self._plot_histogram(axes[0], x, stats)

            # 绘制QQ图
            self._plot_qq_plot(axes[1], x)

            # 优化布局
            fig.tight_layout(pad=2.0)

            # 创建并显示canvas
            self._create_matplotlib_canvas(fig)

            logger.info("直方图和QQ图绘制完成")

        except Exception as e:
            logger.error(f"绘制直方图和QQ图失败: {e}")
            QMessageBox.warning(self, '错误', f'绘制统计图形失败: {e}')

    def _cleanup_matplotlib_canvas(self):
        """清理matplotlib画布以防止内存泄漏"""
        if self.canvas:
            self.canvas_layout.removeWidget(self.canvas)
            self.canvas.deleteLater()
            self.canvas = None
        if self.toolbar:
            self.canvas_layout.removeWidget(self.toolbar)
            self.toolbar.deleteLater()
            self.toolbar = None

    def _calculate_statistics(self, x, lower_tol, upper_tol) -> Dict[str, float]:
        """计算统计数据 - 优化版本

        Args:
            x: 数据数组
            lower_tol: 下公差
            upper_tol: 上公差

        Returns:
            包含统计数据的字典
        """
        # 使用numpy的向量化操作提高性能
        x_array = np.asarray(x)
        lower_tol_array = np.asarray(lower_tol)
        upper_tol_array = np.asarray(upper_tol)

        mu = np.mean(x_array)
        sigma = np.std(x_array, ddof=1)  # 使用样本标准差
        lower_spec = np.mean(lower_tol_array)
        upper_spec = np.mean(upper_tol_array)
        target_value = (lower_spec + upper_spec) / 2

        # 计算过程能力指数，添加数值稳定性检查
        if sigma > 1e-10:  # 避免除零错误
            cp = (upper_spec - lower_spec) / (6 * sigma)
            cpk = min((upper_spec - mu) / (3 * sigma), (mu - lower_spec) / (3 * sigma))
        else:
            cp = float('inf')
            cpk = float('inf')

        return {
            'mu': float(mu),
            'sigma': float(sigma),
            'lower_spec': float(lower_spec),
            'upper_spec': float(upper_spec),
            'target_value': float(target_value),
            'cp': float(cp),
            'cpk': float(cpk)
        }

    def _plot_histogram(self, ax, x, stats):
        """绘制直方图"""
        # 生成正态分布曲线数据
        x_range = np.linspace(np.min(x), np.max(x), 500)  # 减少点数以提高性能
        y = norm.pdf(x_range, stats['mu'], stats['sigma'])

        # 绘制直方图
        ax.hist(x, bins=30, density=True, alpha=0.7, color='#FFA500',
                label='Histogram', edgecolor='black')

        # 绘制正态分布曲线
        ax.plot(x_range, y, color='#0000FF',
                label=f'Normal Distribution (μ={stats["mu"]:.2f}, σ={stats["sigma"]:.2f})')

        # 填充曲线下的区域
        ax.fill_between(x_range, y, color='#ADD8E6', alpha=0.5)

        # 绘制规格线
        ax.axvline(x=stats['lower_spec'], color='#FF0000', linestyle='--', linewidth=1.5,
                   label=f'Lower Spec: {stats["lower_spec"]:.2f}')
        ax.axvline(x=stats['upper_spec'], color='#FF0000', linestyle='--', linewidth=1.5,
                   label=f'Upper Spec: {stats["upper_spec"]:.2f}')
        ax.axvline(x=stats['target_value'], color='#008000', linestyle='--', linewidth=1.5,
                   label=f'Target: {stats["target_value"]:.2f}')

        # 添加过程能力指数
        ax.text(0.02, 0.95, f'Cp: {stats["cp"]:.2f}', transform=ax.transAxes,
                verticalalignment='top', bbox=dict(facecolor='white', alpha=0.7), fontsize=6)
        ax.text(0.02, 0.85, f'Cpk: {stats["cpk"]:.2f}', transform=ax.transAxes,
                verticalalignment='top', bbox=dict(facecolor='white', alpha=0.7), fontsize=6)

        # 设置标题和标签
        ax.set_title('Capability Histogram with Normal Distribution and Spec Limits', fontsize=6)
        ax.set_xlabel('Deviation', fontsize=6)
        ax.set_ylabel('Probability Density', fontsize=6)
        ax.tick_params(axis='both', labelsize=6)
        ax.legend(fontsize=6, loc='upper right')
        ax.grid(True, linestyle='--', alpha=0.7)

    def _plot_qq_plot(self, ax, x):
        """绘制QQ图"""
        from scipy.stats import probplot, anderson

        # 进行 Anderson-Darling 检验
        result = anderson(x, dist='norm')
        ad_statistic = result.statistic
        significance_level_index = 2  # 5% 显著性水平
        critical_value = result.critical_values[significance_level_index]

        # 绘制 QQ 图
        probplot(x, dist="norm", plot=ax)
        ax.set_title('Normal Probability Plot (QQ-Plot)', fontsize=6)
        ax.set_xlabel('Theoretical Quantiles', fontsize=6)
        ax.set_ylabel('Sample Quantiles', fontsize=6)
        ax.tick_params(axis='both', labelsize=6)

        # 添加统计信息
        ax.text(0.02, 0.95, f'AD: {ad_statistic:.2f}', transform=ax.transAxes,
                verticalalignment='top', bbox=dict(facecolor='white', alpha=0.7), fontsize=6)
        ax.text(0.02, 0.85, f'Critical Value (5%): {critical_value:.2f}', transform=ax.transAxes,
                verticalalignment='top', bbox=dict(facecolor='white', alpha=0.7), fontsize=6)

        decision = "Pass" if ad_statistic < critical_value else "Fail"
        ax.text(0.02, 0.75, f'Decision: {decision}', transform=ax.transAxes,
                verticalalignment='top', bbox=dict(facecolor='white', alpha=0.7), fontsize=6)

        ax.grid(True, linestyle='--', alpha=0.7)

    def _create_matplotlib_canvas(self, fig):
        """创建matplotlib画布"""
        self.canvas = FigureCanvas(fig)
        self.toolbar = NavigationToolbar(self.canvas, self)

        # 添加到布局
        self.canvas_layout.addWidget(self.toolbar)
        self.canvas_layout.addWidget(self.canvas)

        # 显示图形
        self.canvas.draw()

        # 关闭matplotlib图形以释放内存
        plt.close(fig)


    def load_product_names(self, force_reload: bool = False) -> None:
        """加载产品列表到UI

        从配置管理器加载产品配置并更新UI列表。
        如果没有产品配置，显示相应提示信息。

        Args:
            force_reload: 是否强制重新加载，忽略缓存
        """
        try:
            products = self.data_manager.load_products(force_reload=force_reload)
            self.ui.listWidget_product.clear()

            if products:
                for product in products:
                    if product.enabled:  # 只显示启用的产品
                        self.ui.listWidget_product.addItem(product.name)

                enabled_count = sum(1 for p in products if p.enabled)
                logger.info(f"成功加载 {enabled_count}/{len(products)} 个启用的产品到UI")

                if enabled_count == 0:
                    self.ui.listWidget_product.addItem('无启用的产品')
                    error_handler.log_and_show_warning("所有产品都已禁用", self)
            else:
                self.ui.listWidget_product.addItem('无可用产品')
                error_handler.log_and_show_warning("没有找到可用的产品配置", self)

        except Exception as e:
            logger.error(f"加载产品列表到UI失败: {e}")
            self.ui.listWidget_product.clear()
            self.ui.listWidget_product.addItem('加载产品列表失败')
            error_handler.log_and_show_error(e, self, "加载产品列表失败")

    def show_progress_dialog(self, message: str = "处理中，请稍候...", maximum: int = 100) -> QProgressDialog:
        """显示进度对话框 - 改进版本"""
        progress = QProgressDialog(message, None, 0, maximum, self)
        progress.setWindowTitle("进度")
        progress.setWindowModality(Qt.WindowModal)
        progress.setCancelButton(None)
        progress.setMinimumDuration(0)
        progress.setValue(0)

        # 设置更好的尺寸
        progress.setMinimumWidth(400)
        progress.show()
        QtWidgets.QApplication.processEvents()
        return progress

    # ====== 按钮点击事件处理 ======

    def on_search_clicked(self):
        """搜索按钮点击事件处理 - 优化版本"""
        try:
            # 清空相关UI组件
            self._clear_ui_components()

            # 获取选中的产品
            product_name = self._get_selected_product()
            if not product_name:
                return

            # 显示进度对话框
            progress = self.show_progress_dialog("正在搜索文件...", maximum=0)

            try:
                # 获取产品路径
                product_path = self.data_manager.get_product_path(product_name)
                validate_file_exists(product_path)

                # 处理TMP文件
                logger.info(f"开始处理产品 {product_name} 的TMP文件")
                progress.setLabelText("正在处理TMP文件...")
                QtWidgets.QApplication.processEvents()

                oktmp_list, noktmp_list, duplicate_errors = process_tmp_files(product_path)

                if not oktmp_list and not noktmp_list:
                    error_handler.log_and_show_warning(f"在路径 {product_path} 中未找到任何TMP文件", self)
                    return

                # 更新数据管理器
                self.data_manager.last_oktmp_list = oktmp_list

                # 更新UI
                progress.setLabelText("正在更新界面...")
                QtWidgets.QApplication.processEvents()

                self._update_sn_list(oktmp_list)
                self._update_error_display(noktmp_list, duplicate_errors)

                # 显示结果摘要
                self._show_search_results(oktmp_list, noktmp_list, duplicate_errors)

            finally:
                progress.close()

        except Exception as e:
            logger.error(f"搜索处理失败: {e}")
            error_handler.log_and_show_error(e, self, "搜索处理失败")

    def _clear_ui_components(self):
        """清空相关UI组件"""
        self._clear_tree_view()
        self.ui.listWidget_sn.clear()
        self.ui.textEdit.clear()

    def _get_selected_product(self) -> Optional[str]:
        """获取选中的产品名称"""
        selected_items = self.ui.listWidget_product.selectedItems()
        if not selected_items:
            error_handler.log_and_show_warning('请先选择一个产品', self)
            return None

        product_name = selected_items[0].text()
        validate_not_empty(product_name, "产品名称")
        return product_name

    def _show_search_results(self, oktmp_list: List[str], noktmp_list: List[str],
                           duplicate_errors: List[str]):
        """显示搜索结果摘要"""
        if oktmp_list:
            logger.info(f"搜索完成: 找到 {len(oktmp_list)} 个有效文件")
            if noktmp_list or duplicate_errors:
                error_handler.log_and_show_info(
                    f"搜索完成！\n有效文件: {len(oktmp_list)} 个\n"
                    f"异常文件: {len(noktmp_list)} 个\n"
                    f"重复文件: {len(duplicate_errors)} 个",
                    self, "搜索结果"
                )
        else:
            error_handler.log_and_show_warning("未找到有效的TMP文件", self)

    def _clear_tree_view(self):
        """清空树视图"""
        empty_model = QStandardItemModel()
        empty_model.setHorizontalHeaderLabels(['测量点'])
        self.ui.treeView.setModel(empty_model)

    def _update_sn_list(self, oktmp_list: List[str]):
        """更新SN列表"""
        self.ui.listWidget_sn.clear()
        for item in oktmp_list:
            base_name = os.path.splitext(os.path.basename(item))[0]
            self.ui.listWidget_sn.addItem(base_name)

    def _update_error_display(self, noktmp_list: List[str], duplicate_errors: List[str]):
        """更新错误显示"""
        self.ui.textEdit.clear()
        lines = []

        if noktmp_list:
            lines.extend([f'TMP文件异常→{os.path.splitext(os.path.basename(i))[0]}'
                         for i in noktmp_list])

        if duplicate_errors:
            lines.extend([f'TMP文件重复→{name}' for name in duplicate_errors])

        if lines:
            self.ui.textEdit.setPlainText('\n'.join(lines))


    def on_analysis_clicked(self):
        """分析按钮点击事件处理"""
        try:
            # 检查数据是否存在
            if self.data_manager.last_df_tmp is None:
                error_handler.log_and_show_warning('请先执行一次搜索以获取数据', self)
                return

            # 获取选中的测量点
            selected_indexes = self.ui.treeView.selectedIndexes()
            points = [idx.data() for idx in selected_indexes if idx.column() == 0]

            validate_list_not_empty(points, "选中的测量点")

            # 执行绘图分析
            logger.info(f"开始分析 {len(points)} 个测量点: {', '.join(points)}")
            self.init_plot(df=self.data_manager.last_df_tmp, points=points)

            error_handler.log_and_show_info(f"成功完成 {len(points)} 个测量点的分析", self, "分析完成")

        except Exception as e:
            logger.error(f"分析处理失败: {e}")
            error_handler.log_and_show_error(e, self, "分析处理失败")


    # ====== 选择变化事件处理 ======

    def on_product_selection_changed(self):
        """产品选择变化事件处理"""
        # 清空相关UI组件
        self.ui.listWidget_sn.clear()
        self._clear_tree_view()
        logger.info("产品选择已变化，已清空相关显示")

    def on_sn_selection_changed(self):
        """SN选择变化事件处理"""
        try:
            selected_items = self.ui.listWidget_sn.selectedItems()
            if not selected_items:
                self._clear_tree_view()
                return

            selected_sns = [item.text() for item in selected_items]
            validate_list_not_empty(selected_sns, "选中的SN")

            # 检查数据是否存在
            if not self.data_manager.last_oktmp_list:
                error_handler.log_and_show_warning('请先执行一次搜索以获取文件列表', self)
                return

            # 构建SN到路径的映射
            sn_to_path = {
                os.path.splitext(os.path.basename(p))[0]: p
                for p in self.data_manager.last_oktmp_list
            }

            # 获取选中SN对应的文件路径
            selected_paths = []
            missing_sns = []

            for sn in selected_sns:
                if sn in sn_to_path:
                    selected_paths.append(sn_to_path[sn])
                else:
                    missing_sns.append(sn)

            if missing_sns:
                error_handler.log_and_show_warning(
                    f"以下SN对应的文件未找到: {', '.join(missing_sns)}", self
                )

            if not selected_paths:
                error_handler.log_and_show_warning('未找到任何有效的文件路径', self)
                return

            # 读取数据并更新UI
            logger.info(f"读取 {len(selected_paths)} 个文件的数据: {', '.join(selected_sns)}")
            df_read, ms_points = read_tmp(selected_paths)

            if df_read is None or df_read.empty:
                raise DataProcessingError("读取的数据为空")

            if not ms_points:
                raise DataProcessingError("未找到任何测量点")

            self.data_manager.last_df_tmp = df_read

            # 点击缓存已不再需要，因为数据直接传递给回调函数

            # 更新测量点树视图
            self._update_measurement_points_tree(ms_points)
            logger.info(f"成功加载 {len(ms_points)} 个测量点: {', '.join(ms_points[:5])}{'...' if len(ms_points) > 5 else ''}")

        except Exception as e:
            logger.error(f"SN选择处理失败: {e}")
            error_handler.log_and_show_error(e, self, "SN选择处理失败")

    def _update_measurement_points_tree(self, ms_points: List[str]):
        """更新测量点树视图"""
        model = QStandardItemModel()
        model.setHorizontalHeaderLabels(['测量点'])
        for pt in ms_points:
            model.appendRow(QStandardItem(pt))
        self.ui.treeView.setModel(model)


    # ====== 菜单点击事件处理 ======

    def product_path_dialog_clicked(self):
        """产品路径管理对话框"""
        try:
            dlg = ProductPathDialog(parent=self)
            dlg.exec_()
            # 强制重新加载产品列表以确保读取最新配置
            self.load_product_names(force_reload=True)
            # 清空数据缓存，因为产品路径可能已更改
            self.data_manager.clear_cache()
            logger.info("产品路径管理对话框已关闭，重新加载产品列表并清空数据缓存")
        except Exception as e:
            logger.error(f"产品路径管理对话框失败: {e}")
            QMessageBox.warning(self, '错误', f'打开产品路径管理失败: {e}')


    def on_export_report_clicked(self):
        """导出报告主流程 - 优化版本

        包含进度提示和异常处理，改进了用户体验和错误处理。
        """
        try:
            # 1. 验证前置条件
            product = self._validate_export_prerequisites()
            if not product:
                return

            # 2. 获取要导出的测量点
            point_list = self._get_export_points()
            if not point_list:
                return

            # 3. 执行导出流程
            self._execute_export_process(product, point_list)

        except Exception as e:
            logger.error(f"导出报告失败: {e}")
            QMessageBox.warning(self, '错误', f'导出报告失败: {e}')

    def _validate_export_prerequisites(self) -> Optional[str]:
        """验证导出前置条件"""
        # 检查选中产品
        product_items = self.ui.listWidget_product.selectedItems()
        if not product_items:
            QMessageBox.warning(self, '提示', '请先选择产品')
            return None

        # 检查数据
        if self.data_manager.last_df_tmp is None:
            QMessageBox.warning(self, '提示', '请先执行一次显示，确保有数据')
            return None

        return product_items[0].text()

    def _get_export_points(self) -> Optional[List[str]]:
        """获取要导出的测量点列表"""
        dialog = self._create_export_selection_dialog()
        result = dialog.exec_()

        if result == 1:  # 所有项目
            return self._get_all_points()
        elif result == 2:  # 选中项目
            return self._get_selected_points()
        else:  # 取消
            return None

    def _create_export_selection_dialog(self) -> QDialog:
        """创建导出选择对话框"""
        dialog = QDialog(self)
        dialog.setWindowTitle("选择导出项目")
        dialog.setFixedSize(300, 150)

        layout = QVBoxLayout()

        label = QLabel("请选择要导出的项目：", dialog)
        layout.addWidget(label)

        all_button = QPushButton("所有项目", dialog)
        all_button.clicked.connect(lambda: dialog.done(1))
        layout.addWidget(all_button)

        selected_button = QPushButton("选中项目", dialog)
        selected_button.clicked.connect(lambda: dialog.done(2))
        layout.addWidget(selected_button)

        cancel_button = QPushButton("取消", dialog)
        cancel_button.clicked.connect(lambda: dialog.done(0))
        layout.addWidget(cancel_button)

        dialog.setLayout(layout)
        return dialog

    def _get_all_points(self) -> List[str]:
        """获取所有测量点"""
        model = self.ui.treeView.model()
        if not model:
            return []

        point_list = []
        for row in range(model.rowCount()):
            index = model.index(row, 0)
            if index.data():
                point_list.append(index.data())
        return point_list

    def _get_selected_points(self) -> Optional[List[str]]:
        """获取选中的测量点"""
        selected_indexes = self.ui.treeView.selectedIndexes()
        if not selected_indexes:
            QMessageBox.warning(self, '提示', '请先在Point Tree中选择测量点')
            return None
        return [idx.data() for idx in selected_indexes if idx.column() == 0 and idx.data()]

    def _execute_export_process(self, product: str, point_list: List[str]):
        """执行导出流程"""
        # 创建报告目录
        time_now = time.strftime('%Y%m%d%H%M%S')
        report_dir = StyleConfig.get_report_dir(product, time_now)
        cache_dir = os.path.join(report_dir, 'cache')
        os.makedirs(cache_dir, exist_ok=True)

        progress = self.show_progress_dialog("正在导出报告，请稍候...", maximum=len(point_list) + 3)

        try:
            df_tmp = self.data_manager.last_df_tmp

            # 步骤1：统计数据
            progress.setLabelText("正在统计数据...")
            progress.setValue(1)
            QtWidgets.QApplication.processEvents()
            df_spc_data = point_spc(df_tmp, point_list)

            # 步骤2：生成图形
            for idx, pt in enumerate(point_list):
                progress.setLabelText(f"生成图形 {idx+1}/{len(point_list)}...")
                progress.setValue(2 + idx)
                QtWidgets.QApplication.processEvents()
                fig_output(df_tmp, [pt], cache_dir)

            # 步骤3：生成报告
            progress.setLabelText("生成报告...")
            progress.setValue(len(point_list) + 2)
            QtWidgets.QApplication.processEvents()
            report(df_spc_data, point_list, cache_dir, report_dir, product, time_now)

            # 步骤4：保存Excel
            progress.setLabelText("保存Excel...")
            progress.setValue(len(point_list) + 3)
            QtWidgets.QApplication.processEvents()
            save_data(df_tmp, df_spc_data, report_dir, product, time_now)

            QMessageBox.information(self, '提示', f'数据已成功导出到:\n{report_dir}')

        finally:
            progress.close()
            # 清理缓存目录
            if os.path.exists(cache_dir):
                try:
                    shutil.rmtree(cache_dir)
                except Exception as e:
                    logger.warning(f"清理缓存目录失败: {e}")


    def on_product_pareto_clicked(self):
        """帕累托图菜单点击事件处理"""
        try:
            # 检查数据是否存在
            if self.data_manager.last_df_tmp is None:
                QMessageBox.warning(self, '提示', '请先执行一次搜索')
                return

            # 如果窗口已存在且可见，则激活窗口
            if self.pareto_window is not None and self.pareto_window.isVisible():
                self.pareto_window.raise_()
                self.pareto_window.activateWindow()
                return

            # 创建新的帕累托图窗口
            logger.info("创建帕累托图窗口")
            data_handler = DataHandler(self.data_manager.last_df_tmp)
            pareto_figure = ParetoFigure(data_handler.df_pareto)
            self.pareto_window = ParetoChartWindow(data_handler, pareto_figure)
            self.pareto_window.showMaximized()
            logger.info("帕累托图窗口已显示")

        except Exception as e:
            logger.error(f"显示帕累托图失败: {e}")
            show_error(f"显示帕累托图失败: {e}")

    def on_spc_table_clicked(self):
        """SPC统计表格菜单点击事件处理"""
        try:
            # 检查数据是否存在
            if self.data_manager.last_df_tmp is None:
                QMessageBox.warning(self, '提示', '请先执行一次搜索')
                return

            logger.info("显示SPC统计表格")
            self.spc_window = show_spc_table(self.data_manager.last_df_tmp)
            logger.info("SPC统计表格已显示")

        except Exception as e:
            logger.error(f"显示SPC表格失败: {e}")
            QMessageBox.warning(self, '错误', f'显示SPC表格失败: {e}')



# ====== 程序入口 ======
def main():
    """程序主入口 - 优化版本

    改进了错误处理和应用程序初始化流程。
    """
    app = None
    try:
        # 创建应用程序实例
        app = QApplication(sys.argv)
        app.setApplicationName("PyStation")
        app.setApplicationVersion("1.0")

        # 设置应用程序属性
        app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

        # 设置样式表
        try:
            style = qdarkstyle.load_stylesheet(palette=LightPalette)
            app.setStyleSheet(style)
            logger.info("深色主题已加载")
        except Exception as e:
            logger.warning(f"加载样式表失败，使用默认样式: {e}")

        logger.info("应用程序启动")

        # 显示登录窗口
        login = LoginWindow()
        login.show()

        # 运行应用程序事件循环
        exit_code = app.exec_()
        logger.info(f"应用程序正常退出，退出码: {exit_code}")
        sys.exit(exit_code)

    except KeyboardInterrupt:
        logger.info("用户中断程序")
        sys.exit(0)
    except Exception as e:
        logger.critical(f"应用程序启动失败: {e}")
        if app is not None:
            QMessageBox.critical(None, '严重错误', f'应用程序启动失败:\n{e}')
        sys.exit(1)
    finally:
        # 确保资源清理
        if app is not None:
            try:
                app.quit()
            except:
                pass


if __name__ == '__main__':
    main()







