# PyStation 源代码模块汇总文档

## 📋 项目概述

PyStation 是一个基于 PyQt5 的数据分析工具，专门用于产品质量数据的统计分析、SPC控制图生成和帕累托图分析。项目采用模块化设计，提供了完整的数据处理、可视化和报告生成功能。

## 🗂️ 模块结构

### 核心模块

#### 1. `main.py` - 主窗口模块 ⭐
**功能描述：** 应用程序的主入口和核心控制器
- **主要类：** `MainWindow`, `DataManager`, `StyleConfig`
- **核心功能：**
  - 主窗口UI管理和事件处理
  - 数据分析和可视化（直方图、QQ图、散点图）
  - 产品数据搜索和处理
  - 报告导出功能
  - 子窗口管理（帕累托图、SPC表格）
- **优化特性：**
  - 性能优化：数据采样、延迟加载、内存管理
  - 用户体验：进度提示、错误处理、响应式UI
  - 资源管理：自动清理、上下文管理器

#### 2. `config_manager.py` - 配置管理模块 🔧
**功能描述：** 统一的配置文件管理和应用设置
- **主要类：** `ConfigManager`, `ProductConfig`, `AppSettings`
- **核心功能：**
  - JSON配置文件的读写操作
  - 产品路径配置管理
  - 应用程序设置持久化
  - 配置验证和错误处理
- **特性：**
  - 类型安全的配置数据类
  - 缓存机制提高性能
  - 配置文件自动备份

#### 3. `data_utils.py` - 数据处理工具模块 📊
**功能描述：** TMP文件处理和数据分析核心功能
- **主要函数：** `process_tmp_files`, `read_tmp`, `point_spc`, `fig_output`, `report`, `save_data`
- **核心功能：**
  - TMP文件搜索、验证和解析
  - 数据清洗和预处理
  - SPC统计计算（Cp, Cpk, Ca等）
  - 图表生成和报告输出
  - Excel数据导出
- **特性：**
  - 支持多种编码格式
  - 重复文件检测
  - 数据完整性验证

#### 4. `error_handler.py` - 错误处理模块 🛡️
**功能描述：** 统一的错误处理和日志管理
- **主要类：** `ErrorHandler`, 自定义异常类
- **核心功能：**
  - 全局异常处理机制
  - 多级日志记录（文件+控制台）
  - 用户友好的错误提示
  - 数据验证装饰器
- **异常类型：**
  - `ValidationError`: 数据验证错误
  - `DataProcessingError`: 数据处理错误
  - `UIError`: 界面操作错误
  - `ConfigurationError`: 配置错误

### UI模块

#### 5. `Ui_main.py` - 主窗口UI定义 🎨
**功能描述：** PyQt5 UI代码生成文件
- **主要类：** `Ui_MainWindow`
- **核心功能：**
  - 主窗口布局和控件定义
  - 菜单栏和工具栏设置
  - 控件属性和样式配置
- **注意：** 此文件由Qt Designer自动生成，不建议手动修改

#### 6. `login_window.py` - 登录窗口模块 🔐
**功能描述：** 用户身份验证界面
- **主要类：** `LoginWindow`
- **核心功能：**
  - 简单的用户名密码验证
  - 登录成功后启动主窗口
  - 错误提示和用户反馈
- **特性：**
  - 延迟导入避免循环依赖
  - 密码输入保护

#### 7. `product_path_dialog.py` - 产品路径管理对话框 📁
**功能描述：** 产品路径配置的图形化管理界面
- **主要类：** `ProductPathDialog`
- **核心功能：**
  - 产品路径的增删改查
  - 表格形式展示配置数据
  - 路径有效性验证
  - JSON配置文件操作
- **特性：**
  - 实时数据验证
  - 用户友好的操作界面
  - 自动保存功能

### 分析模块

#### 8. `pareto_window.py` - 帕累托图分析模块 📈
**功能描述：** 帕累托图生成和分析功能
- **主要类：** `DataHandler`, `ParetoFigure`, `ParetoChartWindow`
- **核心功能：**
  - 帕累托数据处理和计算
  - 交互式帕累托图绘制
  - 数据表格显示
  - 图表导出功能
- **特性：**
  - 累积百分比计算
  - 动态数据更新
  - 多种导出格式

#### 9. `spc_table_window.py` - SPC统计表格模块 📋
**功能描述：** SPC统计数据的表格显示和分析
- **主要类：** `SpcTableWindow`
- **主要函数：** `calculate_spc_data`, `show_spc_table`
- **核心功能：**
  - SPC统计指标计算
  - 表格形式数据展示
  - 统计数据导出
  - 右键菜单操作
- **统计指标：**
  - Cp (过程能力指数)
  - Cpk (过程能力指数)
  - Ca (偏移系数)
  - 最大值、最小值、均值、标准差

## 🔄 模块依赖关系

```
main.py (主控制器)
├── Ui_main.py (UI定义)
├── config_manager.py (配置管理)
├── data_utils.py (数据处理)
├── error_handler.py (错误处理)
├── login_window.py (登录界面)
├── product_path_dialog.py (路径管理)
├── pareto_window.py (帕累托分析)
└── spc_table_window.py (SPC表格)
```

## 🚀 主要特性

### 性能优化
- **数据采样：** 大数据集自动采样提高绘图性能
- **延迟加载：** UI组件延迟初始化避免启动阻塞
- **内存管理：** 自动资源清理和垃圾回收
- **缓存机制：** 配置和计算结果缓存

### 用户体验
- **进度提示：** 长时间操作显示详细进度
- **错误处理：** 友好的错误提示和恢复机制
- **响应式UI：** 防止界面冻结的异步处理
- **主题支持：** 深色/浅色主题切换

### 数据处理
- **多格式支持：** 支持多种编码的TMP文件
- **数据验证：** 完整的数据完整性检查
- **统计分析：** 专业的SPC统计计算
- **可视化：** 丰富的图表类型和交互功能

## 📝 开发规范

### 代码风格
- 遵循PEP 8 Python编码规范
- 使用类型提示提高代码可读性
- 详细的文档字符串和注释
- 模块化设计便于维护和扩展

### 错误处理
- 统一的异常处理机制
- 详细的日志记录
- 用户友好的错误提示
- 优雅的错误恢复

### 测试建议
- 单元测试覆盖核心功能
- 集成测试验证模块协作
- 性能测试确保大数据处理能力
- 用户界面测试保证交互体验

## 🔧 配置文件

- `configs/products.json`: 产品路径配置
- `configs/app_settings.json`: 应用程序设置
- `pystation.log`: 应用程序日志文件

## 📦 依赖库

### 核心依赖
- **PyQt5**: GUI框架
- **pandas**: 数据处理
- **numpy**: 数值计算
- **matplotlib**: 图表绘制
- **scipy**: 科学计算

### 文档生成
- **python-docx**: Word文档生成
- **openpyxl**: Excel文件操作

### 样式主题
- **qdarkstyle**: 深色主题支持

---

*最后更新：2025-08-01*
*版本：v1.0*
