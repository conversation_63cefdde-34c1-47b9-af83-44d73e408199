from argparse import Namespace
from collections import OrderedDict
from time import perf_counter
from pyqtgraph.Qt import QtCore

# Avoid clash with module name
examples_ = OrderedDict([
    ('Command-line usage', 'CLIexample.py'),
    ('Basic Plotting', Namespace(filename='Plotting.py', recommended=True)),
    ('ImageView', Namespace(filename='ImageView.py', recommended=True)),
    ('ParameterTree', Namespace(filename='parametertree.py', recommended=True)),
    ('Plotting Datasets', 'MultiDataPlot.py'),
    ('Parameter-Function Interaction', 'InteractiveParameter.py'),
    ('Crosshair / Mouse interaction', 'crosshair.py'),
    ('Data Slicing', 'DataSlicing.py'),
    ('Plot Customization', 'customPlot.py'),
    ('Timestamps on x axis', 'DateAxisItem.py'),
    ('Image Analysis', Namespace(filename='imageAnalysis.py', recommended=True)),
    ('Matrix Display', 'MatrixDisplayExample.py'),
    ('ViewBox Features', Namespace(filename='ViewBoxFeatures.py', recommended=True)),
    ('Dock widgets', 'dockarea.py'),
    ('Console', 'ConsoleWidget.py'),
    ('Console - Exception inspection', 'console_exception_inspection.py'),
    ('Rich Jupyter Console', 'jupyter_console_example.py'),
    ('Histograms', 'histogram.py'),
    ('Beeswarm plot', 'beeswarm.py'),
    ('Symbols', 'Symbols.py'),
    ('Auto-range', 'PlotAutoRange.py'),
    ('Remote Plotting', 'RemoteSpeedTest.py'),
    ('Scrolling plots', 'scrollingPlots.py'),
    ('HDF5 big data', 'hdf5.py'),
    ('Glow', 'glow.py'),
    ('Demos', OrderedDict([
        ('Optics', 'optics_demos.py'),
        ('Special relativity', 'relativity_demo.py'),
        ('Verlet chain', 'verlet_chain_demo.py'),
        ('Koch Fractal', 'fractal.py'),
    ])),
    ('Colors', OrderedDict([
        ('Color Maps', 'colorMaps.py'),
        ('Color Map Linearization', 'colorMapsLinearized.py'),
        ('Color Gradient Plots', 'ColorGradientPlots.py')
    ])),
    ('GraphicsItems', OrderedDict([
        ('Scatter Plot', 'ScatterPlot.py'),
        #('PlotItem', 'PlotItem.py'),
        ('InfiniteLine', 'InfiniteLine.py'),
        ('IsocurveItem', 'isocurve.py'),
        ('GraphItem', 'GraphItem.py'),
        ('ErrorBarItem', 'ErrorBarItem.py'),
        ('FillBetweenItem', 'FillBetweenItem.py'),
        ('ImageItem - video', 'ImageItem.py'),
        ('ImageItem - draw', 'Draw.py'),
        ('ColorBarItem','ColorBarItem.py'),
        ('Non-uniform Image', 'NonUniformImage.py'),
        ('Region-of-Interest', 'ROIExamples.py'),
        ('Bar Graph', 'BarGraphItem.py'),
        ('GraphicsLayout', 'GraphicsLayout.py'),
        ('LegendItem', 'Legend.py'),
        ('Text Item', 'text.py'),
        ('Linked Views', 'linkedViews.py'),
        ('Arrow', 'Arrow.py'),
        ('ViewBox', 'ViewBoxFeatures.py'),
        ('AxisItem - label overlap', 'AxisItem_label_overlap.py'),
        ('Custom Graphics', 'customGraphicsItem.py'),
        ('Labeled Graph', 'CustomGraphItem.py'),
        ('PColorMeshItem', 'PColorMeshItem.py'),
    ])),
    ('Benchmarks', OrderedDict([
        ('Video speed test', 'VideoSpeedTest.py'),
        ('Line Plot update', 'PlotSpeedTest.py'),
        ('Scatter Plot update', 'ScatterPlotSpeedTest.py'),
        ('Multiple plots', 'MultiPlotSpeedTest.py'),
    ])),
    ('3D Graphics', OrderedDict([
        ('Volumetric', 'GLVolumeItem.py'),
        ('Isosurface', 'GLIsosurface.py'),
        ('Surface Plot', 'GLSurfacePlot.py'),
        ('Scatter Plot', 'GLScatterPlotItem.py'),
        ('Shaders', 'GLshaders.py'),
        ('Line Plot', 'GLLinePlotItem.py'),
        ('Mesh', 'GLMeshItem.py'),
        ('Image', 'GLImageItem.py'),
        ('Text', 'GLTextItem.py'),
        ('BarGraph', 'GLBarGraphItem.py'),
        ('Painter', 'GLPainterItem.py'),
        ('Gradient Legend', 'GLGradientLegendItem.py')
    ])),
    ('Widgets', OrderedDict([
        ('PlotWidget', 'PlotWidget.py'),
        ('SpinBox', 'SpinBox.py'),
        ('ConsoleWidget', 'ConsoleWidget.py'),
        ('Histogram / lookup table', 'HistogramLUT.py'),
        ('TreeWidget', 'TreeWidget.py'),
        ('ScatterPlotWidget', 'ScatterPlotWidget.py'),
        ('DataTreeWidget', 'DataTreeWidget.py'),
        ('GradientWidget', 'GradientWidget.py'),
        ('TableWidget', 'TableWidget.py'),
        ('ColorButton', 'ColorButton.py'),
        #('CheckTable', '../widgets/CheckTable.py'),
        #('VerticalLabel', '../widgets/VerticalLabel.py'),
        ('JoystickButton', 'JoystickButton.py'),
    ])),
    ('Flowcharts', 'Flowchart.py'),
    ('Custom Flowchart Nodes', 'FlowchartCustomNode.py'),
])


# don't care about ordering
# but actually from Python 3.7, dict is ordered
others = dict([
    ('logAxis', 'logAxis.py'),
    ('PanningPlot', 'PanningPlot.py'),
    ('MultiplePlotAxes', 'MultiplePlotAxes.py'),
    ('ROItypes', 'ROItypes.py'),
    ('ScaleBar', 'ScaleBar.py'),
    ('ViewBox', 'ViewBox.py'),
    ('GradientEditor', 'GradientEditor.py'),
    ('GLViewWidget', 'GLViewWidget.py'),
    ('DiffTreeWidget', 'DiffTreeWidget.py'),
    ('RemoteGraphicsView', 'RemoteGraphicsView.py'),
    ('contextMenu', 'contextMenu.py'),
    ('designerExample', 'designerExample.py'),
    ('DateAxisItem_QtDesigner', 'DateAxisItem_QtDesigner.py'),
    ('GraphicsScene', 'GraphicsScene.py'),
    ('MouseSelection', 'MouseSelection.py'),
])


# examples that are subsumed into other examples
trivial = dict([
    ('SimplePlot', 'SimplePlot.py'),    # Plotting.py
    ('LogPlotTest', 'LogPlotTest.py'),  # Plotting.py
    ('ViewLimits', 'ViewLimits.py'),    # ViewBoxFeatures.py
])

# examples that are not suitable for CI testing
skiptest = dict([
    ('ProgressDialog', 'ProgressDialog.py'),    # modal dialog
])


class FrameCounter(QtCore.QObject):
    sigFpsUpdate = QtCore.Signal(object)

    def __init__(self, interval=1000):
        super().__init__()
        self.count = 0
        self.last_update = 0
        self.interval = interval

    def update(self):
        self.count += 1

        if self.last_update == 0:
            self.last_update = perf_counter()
            self.startTimer(self.interval)

    def timerEvent(self, evt):
        now = perf_counter()
        elapsed = now - self.last_update
        fps = self.count / elapsed
        self.last_update = now
        self.count = 0
        self.sigFpsUpdate.emit(fps)
