<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1042</width>
    <height>739</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowTitle">
   <string>Data Center</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QFormLayout" name="formLayout">
    <item row="0" column="0" colspan="2">
     <layout class="QGridLayout" name="gridLayout">
      <item row="5" column="0">
       <widget class="QLabel" name="label_4">
        <property name="text">
         <string>Point tree:</string>
        </property>
       </widget>
      </item>
      <item row="4" column="0">
       <widget class="QPushButton" name="pushButton_serach">
        <property name="text">
         <string>Serach</string>
        </property>
       </widget>
      </item>
      <item row="8" column="1">
       <widget class="QLabel" name="label_3">
        <property name="text">
         <string>Output:</string>
        </property>
       </widget>
      </item>
      <item row="6" column="0" rowspan="5">
       <widget class="QTreeView" name="treeView">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>390</width>
          <height>300</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>390</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="horizontalScrollBarPolicy">
         <enum>Qt::ScrollBarAsNeeded</enum>
        </property>
        <property name="selectionMode">
         <enum>QAbstractItemView::ExtendedSelection</enum>
        </property>
        <property name="horizontalScrollMode">
         <enum>QAbstractItemView::ScrollPerPixel</enum>
        </property>
       </widget>
      </item>
      <item row="1" column="1" rowspan="6">
       <widget class="PlotWidget" name="PlotWidget">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
       </widget>
      </item>
      <item row="10" column="1">
       <widget class="QTextEdit" name="textEdit">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>626</width>
          <height>150</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>150</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="label">
        <property name="text">
         <string>Product：</string>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QListWidget" name="listWidget_sn">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>390</width>
          <height>200</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>390</width>
          <height>300</height>
         </size>
        </property>
        <property name="selectionMode">
         <enum>QAbstractItemView::ExtendedSelection</enum>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QLabel" name="label_5">
        <property name="text">
         <string>Analysis</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QListWidget" name="listWidget_product">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>390</width>
          <height>50</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>390</width>
          <height>50</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="label_2">
        <property name="text">
         <string>Serial Number:</string>
        </property>
       </widget>
      </item>
      <item row="11" column="0">
       <widget class="QPushButton" name="pushButton_analysis">
        <property name="text">
         <string>Analysis</string>
        </property>
       </widget>
      </item>
      <item row="7" column="1">
       <widget class="QWidget" name="widget_plot" native="true">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
       </widget>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1042</width>
     <height>23</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuSetting">
    <property name="title">
     <string>Setting</string>
    </property>
    <addaction name="actionProduct_path"/>
   </widget>
   <widget class="QMenu" name="menuReport">
    <property name="title">
     <string>Report</string>
    </property>
    <addaction name="actionExport_report"/>
    <addaction name="actionProduct_Pareto"/>
    <addaction name="actionProduct_PPK"/>
   </widget>
   <addaction name="menuSetting"/>
   <addaction name="menuReport"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <action name="actionProduct_path">
   <property name="text">
    <string>Product_path</string>
   </property>
  </action>
  <action name="actionExport_report">
   <property name="text">
    <string>Export report
</string>
   </property>
  </action>
  <action name="actionProduct_Pareto">
   <property name="text">
    <string>Product_Pareto</string>
   </property>
  </action>
  <action name="actionProduct_PPK">
   <property name="text">
    <string>Product_PPK</string>
   </property>
  </action>
 </widget>
 <customwidgets>
  <customwidget>
   <class>PlotWidget</class>
   <extends>QGraphicsView</extends>
   <header>pyqtgraph</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
