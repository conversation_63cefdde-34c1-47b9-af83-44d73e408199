"""
配置管理模块
提供统一的配置文件管理、路径处理和应用设置功能
"""

# ============================================================================
# 标准库导入
# ============================================================================
import json
import logging
import os
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
from typing import Dict, List, Any, Optional, Union

# ============================================================================
# 本地模块导入
# ============================================================================
from error_handler import (
    ErrorHandler, ConfigurationError, ValidationError,
    validate_not_empty, validate_file_exists
)


# 默认配置常量
DEFAULT_SETTINGS = {
    "language": "zh_CN",
    "auto_save": True,
    "max_recent_files": 10,
    "log_level": "INFO",
    "window_state": {}
}

DEFAULT_UI_LAYOUT = {}

DEFAULT_USER_PREFERENCES = {}


class ConfigType(Enum):
    """配置类型枚举"""
    PRODUCTS = "products"


@dataclass
class ProductConfig:
    """产品配置数据类"""
    name: str
    path: str
    description: str = ""
    enabled: bool = True
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    
    def __post_init__(self):
        """初始化后验证"""
        validate_not_empty(self.name, "产品名称")
        validate_not_empty(self.path, "产品路径")


@dataclass
class AppSettings:
    """应用设置数据类"""
    language: str = "zh_CN"
    auto_save: bool = True
    max_recent_files: int = 10
    log_level: str = "INFO"
    window_state: Dict[str, Any] = None

    def __post_init__(self):
        if self.window_state is None:
            self.window_state = {}

    @classmethod
    def get_default(cls) -> 'AppSettings':
        """获取默认设置"""
        return cls(**DEFAULT_SETTINGS)


class ConfigManager:
    """配置管理器类

    负责管理应用程序的配置文件，主要是产品配置。
    其他配置（设置、UI布局、用户偏好）已集成到程序中。

    Attributes:
        config_dir: 配置文件目录路径
        error_handler: 错误处理器
        _configs_cache: 配置缓存字典
        _app_settings: 应用设置实例
        _ui_layout: UI布局配置
        _user_preferences: 用户偏好配置
    """

    def __init__(self, config_dir: Optional[str] = None):
        """初始化配置管理器

        Args:
            config_dir: 配置文件目录路径，如果为None则使用默认路径
        """
        self.error_handler = ErrorHandler(__name__)
        self.logger = self.error_handler.logger

        if config_dir is None:
            config_dir = self._get_default_config_dir()

        self.config_dir = Path(config_dir)
        self._configs_cache: Dict[ConfigType, Any] = {}

        # 初始化内置配置
        self._app_settings = AppSettings.get_default()
        self._ui_layout = DEFAULT_UI_LAYOUT.copy()
        self._user_preferences = DEFAULT_USER_PREFERENCES.copy()

        # 确保配置目录存在
        self._ensure_config_dir_exists()

        # 初始化默认配置文件
        self._init_default_configs()
    
    def _get_default_config_dir(self) -> str:
        """获取默认配置目录路径"""
        # 获取项目根目录
        current_file = Path(__file__).resolve()
        project_root = current_file.parent.parent
        return str(project_root / "configs")
    
    def _ensure_config_dir_exists(self) -> None:
        """确保配置目录存在"""
        try:
            self.config_dir.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"配置目录已准备: {self.config_dir}")
        except Exception as e:
            raise ConfigurationError(f"无法创建配置目录 {self.config_dir}: {e}")
    
    def _init_default_configs(self) -> None:
        """初始化默认配置文件"""
        # 只初始化products.json文件
        config_file = self._get_config_file_path(ConfigType.PRODUCTS)
        if not config_file.exists():
            self._save_config_to_file(ConfigType.PRODUCTS, [])
            self.logger.info(f"创建默认配置文件: {config_file}")
    
    def _get_config_file_path(self, config_type: ConfigType) -> Path:
        """获取配置文件路径"""
        filename_map = {
            ConfigType.PRODUCTS: "products.json"
        }
        return self.config_dir / filename_map[config_type]
    
    def load_config(self, config_type: ConfigType, use_cache: bool = True) -> Any:
        """加载配置

        Args:
            config_type: 配置类型
            use_cache: 是否使用缓存

        Returns:
            配置数据

        Raises:
            ConfigurationError: 当配置加载失败时
        """
        # 只有PRODUCTS类型需要从文件加载
        if config_type != ConfigType.PRODUCTS:
            raise ConfigurationError(f"不支持的配置类型: {config_type.value}")

        if use_cache and config_type in self._configs_cache:
            return self._configs_cache[config_type]

        config_file = self._get_config_file_path(config_type)

        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # 验证配置数据
            validated_data = self._validate_config_data(config_type, config_data)

            # 缓存配置
            self._configs_cache[config_type] = validated_data

            self.logger.debug(f"成功加载配置: {config_type.value}")
            return validated_data

        except FileNotFoundError:
            raise ConfigurationError(f"配置文件不存在: {config_file}")
        except json.JSONDecodeError as e:
            raise ConfigurationError(f"配置文件格式错误 {config_file}: {e}")
        except Exception as e:
            raise ConfigurationError(f"加载配置失败 {config_file}: {e}")
    
    def save_config(self, config_type: ConfigType, config_data: Any) -> None:
        """保存配置

        Args:
            config_type: 配置类型
            config_data: 配置数据

        Raises:
            ConfigurationError: 当配置保存失败时
        """
        # 只有PRODUCTS类型需要保存到文件
        if config_type != ConfigType.PRODUCTS:
            raise ConfigurationError(f"不支持的配置类型: {config_type.value}")

        # 验证配置数据
        validated_data = self._validate_config_data(config_type, config_data)

        # 保存到文件
        self._save_config_to_file(config_type, validated_data)

        # 更新缓存
        self._configs_cache[config_type] = validated_data

        self.logger.info(f"配置已保存: {config_type.value}")
    
    def _save_config_to_file(self, config_type: ConfigType, config_data: Any) -> None:
        """保存配置到文件"""
        config_file = self._get_config_file_path(config_type)
        
        try:
            # 创建备份
            if config_file.exists():
                backup_file = config_file.with_suffix('.json.bak')
                config_file.replace(backup_file)
            
            # 保存新配置
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            raise ConfigurationError(f"保存配置文件失败 {config_file}: {e}")
    
    def _validate_config_data(self, config_type: ConfigType, config_data: Any) -> Any:
        """验证配置数据"""
        if config_type == ConfigType.PRODUCTS:
            return self._validate_products_config(config_data)
        else:
            raise ConfigurationError(f"未知的配置类型: {config_type.value}")
    
    def _validate_products_config(self, config_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """验证产品配置"""
        if not isinstance(config_data, list):
            raise ConfigurationError("产品配置必须是列表格式")
        
        validated_products = []
        for i, product_data in enumerate(config_data):
            try:
                # 创建ProductConfig对象进行验证
                product_config = ProductConfig(**product_data)
                validated_products.append(asdict(product_config))
            except Exception as e:
                raise ConfigurationError(f"产品配置 {i} 验证失败: {e}")
        
        return validated_products
    
    def get_products(self) -> List[ProductConfig]:
        """获取产品配置列表"""
        products_data = self.load_config(ConfigType.PRODUCTS)
        return [ProductConfig(**product_data) for product_data in products_data]
    
    def add_product(self, product: ProductConfig) -> None:
        """添加产品配置"""
        products_data = self.load_config(ConfigType.PRODUCTS)
        
        # 检查是否已存在同名产品
        for existing_product in products_data:
            if existing_product['name'] == product.name:
                raise ValidationError(f"产品 {product.name} 已存在")
        
        # 添加时间戳
        import datetime
        now = datetime.datetime.now().isoformat()
        product.created_at = now
        product.updated_at = now
        
        products_data.append(asdict(product))
        self.save_config(ConfigType.PRODUCTS, products_data)
    
    def update_product(self, product_name: str, updated_product: ProductConfig) -> None:
        """更新产品配置"""
        products_data = self.load_config(ConfigType.PRODUCTS)
        
        for i, product_data in enumerate(products_data):
            if product_data['name'] == product_name:
                # 保留创建时间，更新修改时间
                import datetime
                updated_product.created_at = product_data.get('created_at')
                updated_product.updated_at = datetime.datetime.now().isoformat()
                
                products_data[i] = asdict(updated_product)
                self.save_config(ConfigType.PRODUCTS, products_data)
                return
        
        raise ValidationError(f"产品 {product_name} 不存在")
    
    def remove_product(self, product_name: str) -> None:
        """删除产品配置"""
        products_data = self.load_config(ConfigType.PRODUCTS)
        
        for i, product_data in enumerate(products_data):
            if product_data['name'] == product_name:
                products_data.pop(i)
                self.save_config(ConfigType.PRODUCTS, products_data)
                return
        
        raise ValidationError(f"产品 {product_name} 不存在")
    
    def get_settings(self) -> AppSettings:
        """获取应用设置"""
        return self._app_settings

    def update_settings(self, settings: AppSettings) -> None:
        """更新应用设置"""
        self._app_settings = settings
        self.logger.info("应用设置已更新")

    def get_ui_layout(self) -> Dict[str, Any]:
        """获取UI布局配置"""
        return self._ui_layout.copy()

    def update_ui_layout(self, layout: Dict[str, Any]) -> None:
        """更新UI布局配置"""
        self._ui_layout = layout.copy()
        self.logger.info("UI布局配置已更新")

    def get_user_preferences(self) -> Dict[str, Any]:
        """获取用户偏好配置"""
        return self._user_preferences.copy()

    def update_user_preferences(self, preferences: Dict[str, Any]) -> None:
        """更新用户偏好配置"""
        self._user_preferences = preferences.copy()
        self.logger.info("用户偏好配置已更新")
    
    def clear_cache(self) -> None:
        """清空配置缓存"""
        self._configs_cache.clear()
        self.logger.info("配置缓存已清空")
    
    def backup_configs(self, backup_dir: Optional[str] = None) -> str:
        """备份所有配置文件"""
        if backup_dir is None:
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_dir = self.config_dir / f"backup_{timestamp}"
        
        backup_path = Path(backup_dir)
        backup_path.mkdir(parents=True, exist_ok=True)
        
        # 复制所有配置文件
        import shutil
        for config_file in self.config_dir.glob("*.json"):
            if not config_file.name.endswith('.bak'):
                shutil.copy2(config_file, backup_path)
        
        self.logger.info(f"配置文件已备份到: {backup_path}")
        return str(backup_path)


# 全局配置管理器实例
config_manager = ConfigManager()
