"""
配置管理模块 - 简化版
只管理产品路径配置，移除了复杂的配置类型系统和不必要的功能
"""

# ============================================================================
# 标准库导入
# ============================================================================
import json
import logging
from pathlib import Path
from typing import Dict, Optional

# ============================================================================
# 本地模块导入
# ============================================================================
from error_handler import ErrorHandler, ConfigurationError


class ConfigManager:
    """
    只管理产品路径配置
    
    """

    def __init__(self, config_dir: Optional[str] = None):
        """初始化配置管理器

        Args:
            config_dir: 配置文件目录路径，如果为None则使用默认路径
        """
        self.logger = logging.getLogger(__name__)

        if config_dir is None:
            config_dir = self._get_default_config_dir()

        self.config_dir = Path(config_dir)
        self.products_file = self.config_dir / "products.json"

        # 确保配置目录存在
        self._ensure_config_dir_exists()

    def _get_default_config_dir(self) -> str:
        """获取默认配置目录路径"""
        current_file = Path(__file__).resolve()
        project_root = current_file.parent.parent
        return str(project_root / "configs")

    def _ensure_config_dir_exists(self) -> None:
        """确保配置目录存在"""
        try:
            self.config_dir.mkdir(parents=True, exist_ok=True)
            self.logger.debug(f"配置目录已准备: {self.config_dir}")
        except Exception as e:
            raise ConfigurationError(f"无法创建配置目录 {self.config_dir}: {e}")

    def get_products(self) -> Dict[str, str]:
        """获取产品配置字典 {产品名: 路径}

        Returns:
            产品配置字典，如果文件不存在则返回空字典
        """
        if not self.products_file.exists():
            return {}

        try:
            with open(self.products_file, 'r', encoding='utf-8') as f:
                products = json.load(f)

            # 确保返回的是字典格式
            if not isinstance(products, dict):
                self.logger.warning("产品配置格式不正确，返回空字典")
                return {}

            self.logger.debug(f"成功加载 {len(products)} 个产品配置")
            return products

        except json.JSONDecodeError as e:
            self.logger.error(f"产品配置文件格式错误: {e}")
            return {}
        except Exception as e:
            self.logger.error(f"加载产品配置失败: {e}")
            return {}

    def save_products(self, products: Dict[str, str]) -> None:
        """保存产品配置字典

        Args:
            products: 产品配置字典 {产品名: 路径}
        """
        if not isinstance(products, dict):
            raise ConfigurationError("产品配置必须是字典格式")

        try:
            with open(self.products_file, 'w', encoding='utf-8') as f:
                json.dump(products, f, ensure_ascii=False, indent=2)

            self.logger.info(f"成功保存 {len(products)} 个产品配置")

        except Exception as e:
            raise ConfigurationError(f"保存产品配置失败: {e}")

    def add_product(self, name: str, path: str) -> None:
        """添加产品配置

        Args:
            name: 产品名称
            path: 产品路径
        """
        if not name or not name.strip():
            raise ConfigurationError("产品名称不能为空")
        if not path or not path.strip():
            raise ConfigurationError("产品路径不能为空")

        products = self.get_products()

        if name in products:
            raise ConfigurationError(f"产品 '{name}' 已存在")

        products[name] = path.strip()
        self.save_products(products)

    def update_product(self, name: str, new_path: str) -> None:
        """更新产品路径

        Args:
            name: 产品名称
            new_path: 新的产品路径
        """
        if not new_path or not new_path.strip():
            raise ConfigurationError("产品路径不能为空")

        products = self.get_products()

        if name not in products:
            raise ConfigurationError(f"产品 '{name}' 不存在")

        products[name] = new_path.strip()
        self.save_products(products)

    def remove_product(self, name: str) -> None:
        """删除产品配置

        Args:
            name: 产品名称
        """
        products = self.get_products()

        if name not in products:
            raise ConfigurationError(f"产品 '{name}' 不存在")

        del products[name]
        self.save_products(products)

    def get_product_path(self, name: str) -> str:
        """获取指定产品的路径

        Args:
            name: 产品名称

        Returns:
            产品路径
        """
        products = self.get_products()

        if name not in products:
            raise ConfigurationError(f"产品 '{name}' 不存在")

        return products[name]


# 全局配置管理器实例
config_manager = ConfigManager()
