"""
数据处理工具模块
提供TMP文件处理、数据分析、报告生成等功能
"""

# ============================================================================
# 标准库导入
# ============================================================================
import os
import subprocess

# ============================================================================
# 第三方库导入
# ============================================================================
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from docx import Document
from docx.enum.table import WD_CELL_VERTICAL_ALIGNMENT, WD_TABLE_ALIGNMENT
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.shared import Inches, Pt

def process_tmp_files(folder_path):
    tmp_files = []
    # 原代码使用os.walk递归遍历子文件夹，现改为仅遍历当前文件夹
    for file in os.listdir(folder_path):  # 遍历当前文件夹下的所有条目（文件+目录）
        file_path = os.path.join(folder_path, file)
        # 仅处理文件且扩展名是.tmp的情况（排除子目录）
        if os.path.isfile(file_path) and file.lower().endswith('.tmp'):
            tmp_files.append(file_path)
    oktmp_list = []
    noktmp_list = []
    name_to_paths = {}
    for file_path in tmp_files:
        name = os.path.splitext(os.path.basename(file_path))[0]
        if name not in name_to_paths:
            name_to_paths[name] = [file_path]
        else:
            name_to_paths[name].append(file_path)
    duplicate_names = [name for name, paths in name_to_paths.items() if len(paths) > 1]
    unique_files = [paths[0] for name, paths in name_to_paths.items() if len(paths) == 1]
    for file_path in unique_files:
        try:
            with open(file_path, 'r', encoding='Windows-1252') as f:
                file_content = f.read()
            file_line_count = file_content.count("FILE:")
            if file_line_count == 1:
                oktmp_list.append(file_path)
            else:
                noktmp_list.append(file_path)
        except Exception:
            noktmp_list.append(file_path)
    return oktmp_list, noktmp_list, duplicate_names

def create_oot(row):
    if row['low_tol'] <= row['deviation'] <= row['up_tol']:
        return 0
    elif row['deviation'] < row['low_tol']:
        return row['deviation'] - row['low_tol']
    elif row['deviation'] > row['up_tol']:
        return row['deviation'] - row['up_tol']
    else:
        return 0

def read_tmp(tmp_list):
    df_read = pd.DataFrame()
    i = 1
    for oktmp in tmp_list:
        with open(oktmp, 'r', encoding='Windows-1252') as file:
            folder_name = oktmp.split(os.path.sep)[-2]
            file_name = oktmp.split(os.path.sep)[-1]
            file_name, file_extension = os.path.splitext(file_name)
            lines = file.readlines()
            dim2_lines = [line.strip() for line in lines if line.startswith('DIM2:')]
            df = pd.DataFrame(dim2_lines, columns=['A'])
            df[['B', 'feature', 'C']] = df['A'].str.split('\x03', expand=True)
            df = df.drop('A', axis=1)
            df[['D', 'dimension']] = df['B'].str.split(': ', expand=True)
            df = df.drop(['B', 'D'], axis=1)
            df[['axis', 'nominal', 'up_tol', 'low_tol_abs', 'measurement']] = df['C'].str.split('\s+', expand=True)
            df = df.drop('C', axis=1)
            df = df[['dimension', 'feature', 'axis', 'nominal', 'up_tol', 'low_tol_abs', 'measurement']]
            df['dimension'] = df['dimension'].astype(str)
            df['feature'] = df['feature'].astype(str)
            df['axis'] = df['axis'].astype(str)
            df['nominal'] = df['nominal'].astype(float).round(3)
            df['up_tol'] = df['up_tol'].astype(float)
            df['low_tol_abs'] = df['low_tol_abs'].astype(float)
            df['measurement'] = df['measurement'].astype(float).round(3)
            df['low_tol'] = df['low_tol_abs'] * -1
            df['deviation'] = (df['measurement'] - df['nominal']).round(3)
            df['point'] = df['dimension'].astype(str) + '.' + df['axis'] + '-nominal-' + df['nominal'].astype(str) + '-feature-' + df['feature'].astype(str)
            df['folder'] = folder_name
            df['sn'] = file_name
            df['snplus'] = i
            df['oot'] = df.apply(create_oot, axis=1)
            df_read = pd.concat([df_read, df], ignore_index=True)
            i += 1
    ms_points = sorted(df_read['point'].drop_duplicates().tolist())
    return df_read, ms_points

def point_spc(df, points):
    df_spc = pd.DataFrame()
    for point in points:
        df_filter = df[df['point'] == point]
        df_filter = df_filter.sort_values(by=['snplus'])
        dv_usl = df_filter['up_tol'].drop_duplicates().values.tolist()[0]
        dv_lsl = df_filter['low_tol'].drop_duplicates().values.tolist()[0]
        dv_max = df_filter['deviation'].max()
        dv_min = df_filter['deviation'].min()
        dv_c = (dv_usl + dv_lsl) / 2
        dv_t = dv_usl - dv_lsl
        dv_mean = df_filter['deviation'].mean()
        dv_std = df_filter['deviation'].std()
        dv_ca = (dv_mean - dv_c) / (dv_t / 2)
        dv_cp = dv_t / (6 * dv_std)
        dv_cpk = dv_cp * (1 - abs(dv_ca))
        data = {
            'point':[point],
            'max':[round(dv_max, 2)],
            'min':[round(dv_min, 2)],
            'mean':[round(dv_mean, 2)],
            'std':[round(dv_std, 2)],
            'ca':[round(dv_ca, 2)],
            'cp':[round(dv_cp, 2)],
            'cpk':[round(dv_cpk, 2)],
            'nominal_quantity':[len(df_filter['nominal'].drop_duplicates().values.tolist())],
            'up_tol_quantity':[len(df_filter['up_tol'].drop_duplicates().values.tolist())],
            'low_tol_quantity':[len(df_filter['low_tol'].drop_duplicates().values.tolist())]
        }
        df_statistics = pd.DataFrame(data)
        df_spc = pd.concat([df_spc, df_statistics], ignore_index=True)
    return df_spc

def fig_output(df, points, cache_dir):
    for point in points:
        df_filter = df[df['point'] == point]
        df_filter = df_filter.sort_values(by=['snplus']).reset_index(drop=True)  # 重置索引
        sn_quantity = len(df_filter['sn'].drop_duplicates().values.tolist())
        fig, ax = plt.subplots()
        fig.set_size_inches(5.6, 1.7)
        ax.set_xlabel('snplus')
        ax.set_ylabel('deviation')
        ax.tick_params(axis='both', labelsize=6)
        ax.set_xticks(np.arange(0, (sn_quantity + 1), 1))
        ax.plot(df_filter['snplus'], df_filter['up_tol'], color='red', linewidth=1, linestyle='--')
        ax.plot(df_filter['snplus'], df_filter['low_tol'], color='red', linewidth=1, linestyle='--')
        ax.plot(df_filter['snplus'], (df_filter['up_tol'] + df_filter['low_tol'])/2, color='black', linewidth=1)
        ax.plot(df_filter['snplus'], df_filter['deviation'], color='blue', linewidth=1, marker='o', markersize=3)
        first_five_indices = df_filter.head(5).index
        for firstIndex in first_five_indices:
            ax.annotate(df_filter.loc[firstIndex, 'deviation'],
                        xy=(df_filter.loc[firstIndex, 'snplus'], df_filter.loc[firstIndex, 'deviation']),
                        xytext=(0, 5), textcoords='offset points', fontsize=7,  rotation=90, color='green')
        last_five_indices = df_filter.tail(5).index
        for lastIndex in last_five_indices:
            ax.annotate(df_filter.loc[lastIndex, 'deviation'],
                        xy=(df_filter.loc[lastIndex, 'snplus'], df_filter.loc[lastIndex, 'deviation']),
                        xytext=(0, 5), textcoords='offset points', fontsize=7,  rotation=90, color='purple')
        fig.savefig(os.path.join(cache_dir, point + ".png"))
        plt.close()

def report(df, points, cache_dir, report_dir, product, time_now):
    document = Document()
    for point in points:
        paragraph = document.add_paragraph(point)
        paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        paragraph = document.add_paragraph()
        paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        run = paragraph.add_run("")
        run.add_picture(os.path.join(cache_dir, point + '.png'), width=Inches(5.86), height=Inches(1.78))
        table = document.add_table(rows=2, cols=7, style="Table Grid")
        spc_dict = df[df['point'] == point].to_dict('list')
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = 'Max'
        hdr_cells[1].text = 'Min'
        hdr_cells[2].text = 'Mean'
        hdr_cells[3].text = 'Std'
        hdr_cells[4].text = 'Ca'
        hdr_cells[5].text = 'Cp'
        hdr_cells[6].text = 'Cpk'
        hdr_cells_1 = table.rows[1].cells
        hdr_cells_1[0].text = str(spc_dict.get('max')[0])
        hdr_cells_1[1].text = str(spc_dict.get('min')[0])
        hdr_cells_1[2].text = str(spc_dict.get('mean')[0])
        hdr_cells_1[3].text = str(spc_dict.get('std')[0])
        hdr_cells_1[4].text = str(spc_dict.get('ca')[0])
        hdr_cells_1[5].text = str(spc_dict.get('cp')[0])
        hdr_cells_1[6].text = str(spc_dict.get('cpk')[0])
        document.add_paragraph()
    for cpk_table in document.tables:
        cpk_table.alignment = WD_TABLE_ALIGNMENT.CENTER
        for cpk_row in cpk_table.rows:
            for cpk_cell in cpk_row.cells:
                cpk_cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
                cpk_cell.paragraphs[0].alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
                paragraphs = cpk_cell.paragraphs
                for paragraph in paragraphs:
                    for run in paragraph.runs:
                        font = run.font
                        font.size = Pt(10)
    save_path = os.path.join(report_dir, f"{product}-{time_now}.docx")
    document.save(save_path)
    subprocess.Popen(['start', save_path], shell=True)

def save_data(df1, df2, report_dir, product, time_now):
    df_deviation = pd.pivot_table(df1, values='deviation', index=['point', 'dimension', 'feature', 'axis', 'nominal', 'up_tol', 'low_tol'], columns=['sn']).reset_index()
    df_result = pd.merge(df2, df_deviation, on='point')
    df_oot = pd.pivot_table(df1[df1['oot'] !=0] , values='oot', index=['point'], columns=['sn']).reset_index()
    with pd.ExcelWriter(os.path.join(report_dir, f"{product}-{time_now}.xlsx")) as writer:
        df1.to_excel(writer, sheet_name='database')
        df_result.to_excel(writer, sheet_name='deviation_data')
        df_oot.to_excel(writer, sheet_name='oot_pivot')
