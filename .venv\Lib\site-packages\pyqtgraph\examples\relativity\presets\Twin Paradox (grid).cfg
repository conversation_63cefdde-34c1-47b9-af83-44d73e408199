name: 'params'
strictNaming: False
default: None
renamable: False
enabled: True
value: None
visible: True
readonly: False
removable: False
type: 'group'
children:
    Load Preset..:
        name: 'Load Preset..'
        limits: ['', 'Twin Paradox (grid)', 'Twin Paradox']
        strictNaming: False
        default: None
        renamable: False
        enabled: True
        value: 'Twin Paradox (grid)'
        visible: True
        readonly: False
        values: []
        removable: False
        type: 'list'
        children:
    Duration:
        name: 'Duration'
        limits: [0.1, None]
        strictNaming: False
        default: 10.0
        renamable: False
        enabled: True
        readonly: False
        value: 27.0
        visible: True
        step: 0.1
        removable: False
        type: 'float'
        children:
    Reference Frame:
        name: 'Reference Frame'
        limits: ['Grid00', 'Grid01', 'Grid02', 'Grid03', 'Grid04', 'Grid05', 'Grid06', 'Grid07', 'Grid08', 'Grid09', '<PERSON>rid<PERSON>', '<PERSON>', '<PERSON>']
        strictNaming: False
        default: None
        renamable: False
        enabled: True
        value: 'Alice'
        visible: True
        readonly: False
        values: []
        removable: False
        type: 'list'
        children:
    Animate:
        name: 'Animate'
        strictNaming: False
        default: True
        renamable: False
        enabled: True
        value: True
        visible: True
        readonly: False
        removable: False
        type: 'bool'
        children:
    Animation Speed:
        name: 'Animation Speed'
        limits: [0.0001, None]
        strictNaming: False
        default: 1.0
        renamable: False
        enabled: True
        readonly: False
        value: 1.0
        visible: True
        step: 0.1
        removable: False
        dec: True
        type: 'float'
        children:
    Recalculate Worldlines:
        name: 'Recalculate Worldlines'
        strictNaming: False
        default: None
        renamable: False
        enabled: True
        value: None
        visible: True
        readonly: False
        removable: False
        type: 'action'
        children:
    Save:
        name: 'Save'
        strictNaming: False
        default: None
        renamable: False
        enabled: True
        value: None
        visible: True
        readonly: False
        removable: False
        type: 'action'
        children:
    Load:
        name: 'Load'
        strictNaming: False
        default: None
        renamable: False
        enabled: True
        value: None
        visible: True
        readonly: False
        removable: False
        type: 'action'
        children:
    Objects:
        name: 'Objects'
        strictNaming: False
        default: None
        renamable: False
        addText: 'Add New..'
        enabled: True
        value: None
        visible: True
        readonly: False
        removable: False
        type: None
        children:
            Grid:
                name: 'Grid'
                strictNaming: False
                default: None
                renamable: True
                enabled: True
                value: None
                visible: True
                readonly: False
                removable: True
                type: 'Grid'
                autoIncrementName: True
                children:
                    Number of Clocks:
                        name: 'Number of Clocks'
                        limits: [1, None]
                        strictNaming: False
                        default: 5
                        renamable: False
                        enabled: True
                        value: 11
                        visible: True
                        readonly: False
                        removable: False
                        type: 'int'
                        children:
                    Spacing:
                        name: 'Spacing'
                        strictNaming: False
                        default: 1.0
                        renamable: False
                        enabled: True
                        readonly: False
                        value: 2.0
                        visible: True
                        step: 0.1
                        removable: False
                        type: 'float'
                        children:
                    ClockTemplate:
                        name: 'ClockTemplate'
                        strictNaming: False
                        default: None
                        renamable: True
                        enabled: True
                        value: None
                        visible: True
                        readonly: False
                        removable: True
                        type: 'Clock'
                        autoIncrementName: True
                        children:
                            Initial Position:
                                name: 'Initial Position'
                                strictNaming: False
                                default: 0.0
                                renamable: False
                                enabled: True
                                readonly: False
                                value: -10.0
                                visible: True
                                step: 0.1
                                removable: False
                                type: 'float'
                                children:
                            Acceleration:
                                name: 'Acceleration'
                                strictNaming: False
                                default: None
                                renamable: False
                                addText: 'Add Command..'
                                enabled: True
                                value: None
                                visible: True
                                readonly: False
                                removable: False
                                type: 'AccelerationGroup'
                                children:
                            Rest Mass:
                                name: 'Rest Mass'
                                limits: [1e-09, None]
                                strictNaming: False
                                default: 1.0
                                renamable: False
                                enabled: True
                                readonly: False
                                value: 1.0
                                visible: True
                                step: 0.1
                                removable: False
                                type: 'float'
                                children:
                            Color:
                                name: 'Color'
                                strictNaming: False
                                default: (100, 100, 150)
                                renamable: False
                                enabled: True
                                value: (77, 77, 77, 255)
                                visible: True
                                readonly: False
                                removable: False
                                type: 'color'
                                children:
                            Size:
                                name: 'Size'
                                strictNaming: False
                                default: 0.5
                                renamable: False
                                enabled: True
                                value: 1.0
                                visible: True
                                readonly: False
                                removable: False
                                type: 'float'
                                children:
                            Vertical Position:
                                name: 'Vertical Position'
                                strictNaming: False
                                default: 0.0
                                renamable: False
                                enabled: True
                                readonly: False
                                value: -2.0
                                visible: True
                                step: 0.1
                                removable: False
                                type: 'float'
                                children:
            Alice:
                name: 'Alice'
                strictNaming: False
                default: None
                renamable: True
                enabled: True
                value: None
                visible: True
                readonly: False
                removable: True
                type: 'Clock'
                autoIncrementName: True
                children:
                    Initial Position:
                        name: 'Initial Position'
                        strictNaming: False
                        default: 0.0
                        renamable: False
                        enabled: True
                        readonly: False
                        value: 0.0
                        visible: True
                        step: 0.1
                        removable: False
                        type: 'float'
                        children:
                    Acceleration:
                        name: 'Acceleration'
                        strictNaming: False
                        default: None
                        renamable: False
                        addText: 'Add Command..'
                        enabled: True
                        value: None
                        visible: True
                        readonly: False
                        removable: False
                        type: 'AccelerationGroup'
                        children:
                            Command:
                                name: 'Command'
                                strictNaming: False
                                default: None
                                renamable: True
                                enabled: True
                                value: None
                                visible: True
                                readonly: False
                                removable: True
                                type: None
                                autoIncrementName: True
                                children:
                                    Proper Time:
                                        name: 'Proper Time'
                                        strictNaming: False
                                        default: 0.0
                                        renamable: False
                                        enabled: True
                                        value: 1.0
                                        visible: True
                                        readonly: False
                                        removable: False
                                        type: 'float'
                                        children:
                                    Acceleration:
                                        name: 'Acceleration'
                                        strictNaming: False
                                        default: 0.0
                                        renamable: False
                                        enabled: True
                                        readonly: False
                                        value: 0.5
                                        visible: True
                                        step: 0.1
                                        removable: False
                                        type: 'float'
                                        children:
                            Command2:
                                name: 'Command2'
                                strictNaming: False
                                default: None
                                renamable: True
                                enabled: True
                                value: None
                                visible: True
                                readonly: False
                                removable: True
                                type: None
                                autoIncrementName: True
                                children:
                                    Proper Time:
                                        name: 'Proper Time'
                                        strictNaming: False
                                        default: 2.0
                                        renamable: False
                                        enabled: True
                                        value: 3.0
                                        visible: True
                                        readonly: False
                                        removable: False
                                        type: 'float'
                                        children:
                                    Acceleration:
                                        name: 'Acceleration'
                                        strictNaming: False
                                        default: 0.0
                                        renamable: False
                                        enabled: True
                                        readonly: False
                                        value: 0.0
                                        visible: True
                                        step: 0.1
                                        removable: False
                                        type: 'float'
                                        children:
                            Command3:
                                name: 'Command3'
                                strictNaming: False
                                default: None
                                renamable: True
                                enabled: True
                                value: None
                                visible: True
                                readonly: False
                                removable: True
                                type: None
                                autoIncrementName: True
                                children:
                                    Proper Time:
                                        name: 'Proper Time'
                                        strictNaming: False
                                        default: 3.0
                                        renamable: False
                                        enabled: True
                                        value: 8.0
                                        visible: True
                                        readonly: False
                                        removable: False
                                        type: 'float'
                                        children:
                                    Acceleration:
                                        name: 'Acceleration'
                                        strictNaming: False
                                        default: 0.0
                                        renamable: False
                                        enabled: True
                                        readonly: False
                                        value: -0.5
                                        visible: True
                                        step: 0.1
                                        removable: False
                                        type: 'float'
                                        children:
                            Command4:
                                name: 'Command4'
                                strictNaming: False
                                default: None
                                renamable: True
                                enabled: True
                                value: None
                                visible: True
                                readonly: False
                                removable: True
                                type: None
                                autoIncrementName: True
                                children:
                                    Proper Time:
                                        name: 'Proper Time'
                                        strictNaming: False
                                        default: 4.0
                                        renamable: False
                                        enabled: True
                                        value: 12.0
                                        visible: True
                                        readonly: False
                                        removable: False
                                        type: 'float'
                                        children:
                                    Acceleration:
                                        name: 'Acceleration'
                                        strictNaming: False
                                        default: 0.0
                                        renamable: False
                                        enabled: True
                                        readonly: False
                                        value: 0.0
                                        visible: True
                                        step: 0.1
                                        removable: False
                                        type: 'float'
                                        children:
                            Command5:
                                name: 'Command5'
                                strictNaming: False
                                default: None
                                renamable: True
                                enabled: True
                                value: None
                                visible: True
                                readonly: False
                                removable: True
                                type: None
                                autoIncrementName: True
                                children:
                                    Proper Time:
                                        name: 'Proper Time'
                                        strictNaming: False
                                        default: 6.0
                                        renamable: False
                                        enabled: True
                                        value: 17.0
                                        visible: True
                                        readonly: False
                                        removable: False
                                        type: 'float'
                                        children:
                                    Acceleration:
                                        name: 'Acceleration'
                                        strictNaming: False
                                        default: 0.0
                                        renamable: False
                                        enabled: True
                                        readonly: False
                                        value: 0.5
                                        visible: True
                                        step: 0.1
                                        removable: False
                                        type: 'float'
                                        children:
                            Command6:
                                name: 'Command6'
                                strictNaming: False
                                default: None
                                renamable: True
                                enabled: True
                                value: None
                                visible: True
                                readonly: False
                                removable: True
                                type: None
                                autoIncrementName: True
                                children:
                                    Proper Time:
                                        name: 'Proper Time'
                                        strictNaming: False
                                        default: 7.0
                                        renamable: False
                                        enabled: True
                                        value: 19.0
                                        visible: True
                                        readonly: False
                                        removable: False
                                        type: 'float'
                                        children:
                                    Acceleration:
                                        name: 'Acceleration'
                                        strictNaming: False
                                        default: 0.0
                                        renamable: False
                                        enabled: True
                                        readonly: False
                                        value: 0.0
                                        visible: True
                                        step: 0.1
                                        removable: False
                                        type: 'float'
                                        children:
                    Rest Mass:
                        name: 'Rest Mass'
                        limits: [1e-09, None]
                        strictNaming: False
                        default: 1.0
                        renamable: False
                        enabled: True
                        readonly: False
                        value: 1.0
                        visible: True
                        step: 0.1
                        removable: False
                        type: 'float'
                        children:
                    Color:
                        name: 'Color'
                        strictNaming: False
                        default: (100, 100, 150)
                        renamable: False
                        enabled: True
                        value: (82, 123, 44, 255)
                        visible: True
                        readonly: False
                        removable: False
                        type: 'color'
                        children:
                    Size:
                        name: 'Size'
                        strictNaming: False
                        default: 0.5
                        renamable: False
                        enabled: True
                        value: 1.5
                        visible: True
                        readonly: False
                        removable: False
                        type: 'float'
                        children:
                    Vertical Position:
                        name: 'Vertical Position'
                        strictNaming: False
                        default: 0.0
                        renamable: False
                        enabled: True
                        readonly: False
                        value: 3.0
                        visible: True
                        step: 0.1
                        removable: False
                        type: 'float'
                        children:
            Bob:
                name: 'Bob'
                strictNaming: False
                default: None
                renamable: True
                enabled: True
                value: None
                visible: True
                readonly: False
                removable: True
                type: 'Clock'
                autoIncrementName: True
                children:
                    Initial Position:
                        name: 'Initial Position'
                        strictNaming: False
                        default: 0.0
                        renamable: False
                        enabled: True
                        readonly: False
                        value: 0.0
                        visible: True
                        step: 0.1
                        removable: False
                        type: 'float'
                        children:
                    Acceleration:
                        name: 'Acceleration'
                        strictNaming: False
                        default: None
                        renamable: False
                        addText: 'Add Command..'
                        enabled: True
                        value: None
                        visible: True
                        readonly: False
                        removable: False
                        type: 'AccelerationGroup'
                        children:
                    Rest Mass:
                        name: 'Rest Mass'
                        limits: [1e-09, None]
                        strictNaming: False
                        default: 1.0
                        renamable: False
                        enabled: True
                        readonly: False
                        value: 1.0
                        visible: True
                        step: 0.1
                        removable: False
                        type: 'float'
                        children:
                    Color:
                        name: 'Color'
                        strictNaming: False
                        default: (100, 100, 150)
                        renamable: False
                        enabled: True
                        value: (69, 69, 126, 255)
                        visible: True
                        readonly: False
                        removable: False
                        type: 'color'
                        children:
                    Size:
                        name: 'Size'
                        strictNaming: False
                        default: 0.5
                        renamable: False
                        enabled: True
                        value: 1.5
                        visible: True
                        readonly: False
                        removable: False
                        type: 'float'
                        children:
                    Vertical Position:
                        name: 'Vertical Position'
                        strictNaming: False
                        default: 0.0
                        renamable: False
                        enabled: True
                        readonly: False
                        value: 0.0
                        visible: True
                        step: 0.1
                        removable: False
                        type: 'float'
                        children:
        addList: ['Clock', 'Grid']
