"""
帕累托图窗口模块
提供帕累托图分析和可视化功能
"""

# ============================================================================
# 标准库导入
# ============================================================================
import sys

# ============================================================================
# 第三方库导入
# ============================================================================
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QAction, QWidget, QVBoxLayout,
    QTableWidget, QTableWidgetItem, QFileDialog, QMessageBox, QMenu
)
from PyQt5.QtCore import Qt


def show_error(msg: str) -> None:
    """弹出错误提示框"""
    QMessageBox.critical(None, "错误", msg)


class DataHandler:
    """负责数据加载和处理"""
    def __init__(self, df: pd.DataFrame):
        self.df = df
        self.df_oot, self.df_pareto = self.process_data(df)

    @staticmethod
    def process_data(df: pd.DataFrame):
        try:
            df_oot = pd.pivot_table(df[df['oot'] != 0], values='deviation', index=['point'], columns=['sn']).reset_index()
            df_tol = df[df['oot'] != 0].groupby('point')[['up_tol', 'low_tol']].first().reset_index()
            df_oot = pd.merge(df_tol, df_oot, on='point', how='left')
            non_point_columns = df_oot.columns.drop('point')
            numeric_columns = df_oot[non_point_columns].select_dtypes(include=['number']).columns
            df_oot[numeric_columns] = df_oot[numeric_columns].round(3)
            df_pareto = df.groupby('point')['oot'].apply(lambda x: (x != 0).sum()).reset_index(name='non_zero_count')
            df_pareto = df_pareto[df_pareto['non_zero_count'] != 0]
            df_pareto = df_pareto.sort_values(by='non_zero_count', ascending=False).reset_index(drop=True)
            df_oot = pd.merge(df_pareto, df_oot, on='point', how='left')
            df_pareto['cumulative_percentage'] = (df_pareto['non_zero_count'].cumsum() / df_pareto['non_zero_count'].sum()) * 100
            return df_oot, df_pareto
        except Exception as e:
            raise RuntimeError(f"数据处理失败: {e}")


def select_excel_file(parent=None) -> str:
    """弹出文件选择对话框，返回选中的Excel文件路径"""
    file_path, _ = QFileDialog.getOpenFileName(parent, "选择 Excel 文件", "", "Excel Files (*.xlsx)")
    if not file_path:
        raise RuntimeError("未选择文件，程序退出。")
    return file_path


class ParetoFigure:
    """负责帕累托图的绘制"""
    def __init__(self, df_pareto: pd.DataFrame):
        self.fig, self.ax1, self.ax2, self.bars = self.create_pareto_figure(df_pareto)

    @staticmethod
    def create_pareto_figure(df_pareto: pd.DataFrame):
        try:
            fig, ax1 = plt.subplots()
            num_bars = len(df_pareto['point'])
            try:
                colors = plt.get_cmap('plasma')(np.linspace(0, 1, num_bars))
            except Exception:
                colors = plt.get_cmap('viridis')(np.linspace(0, 1, num_bars))
            bars = ax1.bar(df_pareto['point'], df_pareto['non_zero_count'], color=colors, edgecolor='black')
            ax1.set_xlabel('Point')
            ax1.set_ylabel('Non Zero Count', color='b')
            ax1.tick_params(axis='y', labelcolor='b')
            for bar in bars:
                height = bar.get_height()
                ax1.annotate(f'{height}', xy=(bar.get_x() + bar.get_width() / 2, height),
                             xytext=(0, 3), textcoords='offset points', ha='center', va='bottom')
            ax2 = ax1.twinx()
            ax2.plot(df_pareto['point'], df_pareto['cumulative_percentage'], color='r', marker='o')
            ax2.set_ylabel('Cumulative Percentage (%)', color='r')
            ax2.tick_params(axis='y', labelcolor='r')
            ax2.axhline(y=80, color='g', linestyle='--', label='80%')
            ax2.legend(loc='upper right')
            plt.xticks(rotation=90)
            ax1.get_xaxis().set_visible(False)
            ax2.get_xaxis().set_visible(False)
            plt.title('Pareto Chart')
            return fig, ax1, ax2, bars
        except Exception as e:
            raise RuntimeError(f"绘图失败: {e}")


class ParetoChartWindow(QWidget):
    """帕累托主界面窗口"""
    def __init__(self, data_handler: DataHandler, pareto_figure: ParetoFigure):
        super().__init__()
        self.df = data_handler.df
        self.df_oot = data_handler.df_oot
        self.df_pareto = data_handler.df_pareto
        self.fig = pareto_figure.fig
        self.ax1 = pareto_figure.ax1
        self.ax2 = pareto_figure.ax2
        self.bars = pareto_figure.bars
        self.graph_windows = []
        self.initUI()

    def initUI(self) -> None:
        self.canvas = FigureCanvas(self.fig)
        self.canvas.setFixedHeight(600)
        from matplotlib.backends.backend_qt import NavigationToolbar2QT as NavigationToolbar
        self.toolbar = NavigationToolbar(self.canvas, self)

        # 创建搜索框
        from PyQt5.QtWidgets import QHBoxLayout, QLineEdit, QLabel, QPushButton
        search_layout = QHBoxLayout()
        search_label = QLabel("搜索:")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入搜索关键词...")
        self.search_input.textChanged.connect(self.filter_table)
        clear_button = QPushButton("清除")
        clear_button.clicked.connect(self.clear_search)
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(clear_button)
        search_layout.addStretch()  # 添加弹性空间

        self.table = QTableWidget()
        self.table.setRowCount(self.df_oot.shape[0])
        self.table.setColumnCount(self.df_oot.shape[1])
        self.table.setHorizontalHeaderLabels(self.df_oot.columns)

        # 存储原始数据用于搜索
        self.original_data = []
        for row in range(self.df_oot.shape[0]):
            row_data = []
            for col in range(self.df_oot.shape[1]):
                item_text = str(self.df_oot.iloc[row, col])
                item = QTableWidgetItem(item_text)
                item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                self.table.setItem(row, col, item)
                row_data.append(item_text)
            self.original_data.append(row_data)

        self.table.resizeColumnsToContents()
        self.table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self.show_table_menu)
        self.table.setFixedHeight(400)

        layout = QVBoxLayout()
        layout.addWidget(self.toolbar)
        layout.addWidget(self.canvas)
        layout.addLayout(search_layout)
        layout.addWidget(self.table)
        self.canvas.mpl_connect('motion_notify_event', self.on_hover)
        self.setLayout(layout)

    def on_hover(self, event) -> None:
        from PyQt5.QtWidgets import QToolTip
        from PyQt5.QtGui import QCursor
        if event.inaxes not in [self.ax1, self.ax2]:
            return
        for bar in self.bars:
            contains, _ = bar.contains(event)
            if contains:
                idx = list(self.bars).index(bar)
                point_name = self.df_pareto['point'].iloc[idx]
                count = self.df_pareto['non_zero_count'].iloc[idx]
                QToolTip.showText(
                    QCursor.pos(),
                    f'检测点: {point_name}\n不合格数: {count}'
                )
                return
        line = self.ax2.lines[0]
        contains, props = line.contains(event)
        if contains:
            ind = props.get('ind', [])
            if ind:
                idx = ind[0]
                point_name = self.df_pareto['point'].iloc[idx]
                percentage = self.df_pareto['cumulative_percentage'].iloc[idx]
                QToolTip.showText(
                    QCursor.pos(),
                    f'检测点: {point_name}\n累计占比: {percentage:.1f}%'
                )
                return
        QToolTip.hideText()
        self.setWindowTitle('Product Pareto Chart and Table')

    def show_table_menu(self, pos) -> None:
        menu = QMenu(self.table)
        copy_action = QAction("复制所选内容", self.table)
        copy_action.triggered.connect(self.copy_selected_content)
        menu.addAction(copy_action)

        # 添加保存到CSV功能
        save_csv_action = QAction("保存表格到CSV", self.table)
        save_csv_action.triggered.connect(self.save_table_to_csv)
        menu.addAction(save_csv_action)
        # 支持多选point列右键graph
        selected_items = self.table.selectedItems()
        point_indices = []
        for item in selected_items:
            col = item.column()
            header_item = self.table.horizontalHeaderItem(col)
            if header_item is not None and header_item.text() == 'point':
                point_indices.append(item)
        if point_indices:
            graph_action = QAction("graph", self.table)
            def show_all_graphs():
                for item in point_indices:
                    self.show_graph(item.text())
            graph_action.triggered.connect(show_all_graphs)
            menu.addAction(graph_action)
        menu.exec_(self.table.mapToGlobal(pos))

    def copy_selected_content(self) -> None:
        from PyQt5.QtWidgets import QApplication

        # 获取选中的项目
        selected_items = self.table.selectedItems()
        if not selected_items:
            return

        # 获取选中项目的行列信息
        selected_ranges = {}
        for item in selected_items:
            row = item.row()
            col = item.column()
            if row not in selected_ranges:
                selected_ranges[row] = []
            selected_ranges[row].append(col)

        # 按行排序
        sorted_rows = sorted(selected_ranges.keys())

        content = ""
        for i, row in enumerate(sorted_rows):
            # 按列排序
            sorted_cols = sorted(selected_ranges[row])
            row_data = []
            for col in sorted_cols:
                item = self.table.item(row, col)
                row_data.append(item.text() if item else "")
            content += "\t".join(row_data)
            if i < len(sorted_rows) - 1:  # 不是最后一行
                content += "\n"

        clipboard = QApplication.clipboard()
        clipboard.setText(content)

    def save_table_to_csv(self) -> None:
        """保存表格数据到CSV文件"""
        from PyQt5.QtWidgets import QFileDialog, QMessageBox
        import csv
        import os

        try:
            # 弹出文件保存对话框
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "保存表格到CSV",
                "pareto_data.csv",
                "CSV Files (*.csv);;All Files (*)"
            )

            if not file_path:
                return  # 用户取消了保存

            # 确保文件扩展名为.csv
            if not file_path.lower().endswith('.csv'):
                file_path += '.csv'

            # 写入CSV文件
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)

                # 写入列名
                headers = []
                for col in range(self.table.columnCount()):
                    header_item = self.table.horizontalHeaderItem(col)
                    if header_item is not None:
                        headers.append(header_item.text())
                    else:
                        headers.append(f"Column_{col}")
                writer.writerow(headers)

                # 写入数据行
                for row in range(self.table.rowCount()):
                    row_data = []
                    for col in range(self.table.columnCount()):
                        item = self.table.item(row, col)
                        row_data.append(item.text() if item else "")
                    writer.writerow(row_data)

            QMessageBox.information(self, "保存成功", f"表格已成功保存到:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存CSV文件时发生错误:\n{str(e)}")

    def filter_table(self):
        """根据搜索关键词过滤表格行"""
        search_text = self.search_input.text().lower()

        if not search_text:
            # 如果搜索框为空，显示所有行
            for row in range(self.table.rowCount()):
                self.table.setRowHidden(row, False)
            return

        # 遍历所有行，检查是否包含搜索关键词
        for row in range(self.table.rowCount()):
            row_contains_text = False
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                if item and search_text in item.text().lower():
                    row_contains_text = True
                    break

            # 隐藏或显示行
            self.table.setRowHidden(row, not row_contains_text)

    def clear_search(self):
        """清除搜索框并显示所有行"""
        self.search_input.clear()
        for row in range(self.table.rowCount()):
            self.table.setRowHidden(row, False)

    def show_graph(self, point: str) -> None:
        try:
            graph_window = GraphWindow(self.df, point)
            graph_window.show()
            self.graph_windows.append(graph_window)
        except Exception as e:
            show_error(f"显示详细图形失败: {e}")


class GraphWindow(QWidget):
    """详细点图窗口"""
    def __init__(self, df: pd.DataFrame, point: str):
        super().__init__()
        self.ax = None
        self.df = df
        self.point = point
        self.initUI()

    def initUI(self) -> None:
        try:
            fig, ax, line = self.fig_output()
            self.ax = ax
            self.canvas = FigureCanvas(fig)
            from matplotlib.backends.backend_qt import NavigationToolbar2QT as NavigationToolbar
            self.toolbar = NavigationToolbar(self.canvas, self)
            self.canvas.mpl_connect('motion_notify_event', self.on_hover_2)
            layout = QVBoxLayout()
            layout.addWidget(self.toolbar)
            layout.addWidget(self.canvas)
            self.setLayout(layout)
            self.setWindowTitle(f"Graph for {self.point}")
        except Exception as e:
            show_error(f"详细图形窗口初始化失败: {e}")

    def fig_output(self):
        try:
            df_filter = self.df[self.df['point'] == self.point].sort_values(by=['snplus'])
            sn_quantity = len(df_filter['sn'].drop_duplicates().values.tolist())
            fig, ax = plt.subplots()
            fig.set_size_inches(13.66/2, 7.68/2)
            ax.set_xlabel('snplus')
            ax.set_ylabel('deviation')
            ax.tick_params(axis='both', labelsize=6)
            ax.set_xticks(np.arange(0, (sn_quantity + 1), 1))
            line, = ax.plot(df_filter['snplus'], df_filter['deviation'], color='blue', linewidth=2, marker='o', markersize=8)
            ax.plot(df_filter['snplus'], df_filter['up_tol'], color='red', linewidth=2, linestyle='--')
            ax.plot(df_filter['snplus'], df_filter['low_tol'], color='red', linewidth=2, linestyle='--')
            ax.plot(df_filter['snplus'], (df_filter['up_tol'] + df_filter['low_tol']) / 2, color='green', linewidth=2)
            ax.plot(df_filter['snplus'], df_filter['up_tol']*0.8, color='orange', linewidth=1, linestyle='--')
            ax.plot(df_filter['snplus'], df_filter['low_tol']*0.8, color='orange', linewidth=1, linestyle='--')
            return fig, ax, line
        except Exception as e:
            show_error(f"图形生成失败: {e}")
            raise

    def on_hover_2(self, event) -> None:
        """鼠标悬停在折线图上时显示详细数据提示，仅显示最近的偏差值点。"""
        from PyQt5.QtWidgets import QToolTip
        from PyQt5.QtGui import QCursor
        if event.inaxes != self.ax:
            QToolTip.hideText()
            return
        df_filter = self.df[self.df['point'] == self.point].sort_values(by=['snplus'])
        threshold = 0.05  # 鼠标与点的最大距离
        if self.ax is not None and hasattr(self.ax, 'lines'):
            # 只处理偏差值（第0条线）
            if len(self.ax.lines) > 0:
                line = self.ax.lines[0]
                x_data, y_data = line.get_data()
                x_event, y_event = event.xdata, event.ydata
                if x_event is not None and y_event is not None:
                    distances = np.sqrt((x_data - x_event) ** 2 + (y_data - y_event) ** 2)
                    min_distance = np.min(distances)
                    if min_distance < threshold:
                        idx = np.argmin(distances)
                        label = '偏差值'
                        y = df_filter['deviation'].iloc[idx]
                        x = df_filter['snplus'].iloc[idx]
                        sn = df_filter['sn'].iloc[idx]
                        QToolTip.showText(
                            QCursor.pos(),
                            f'{label}\nx: {x}\ny: {y:.3f}\nsn: {sn}'
                        )
                        return
        QToolTip.hideText()
