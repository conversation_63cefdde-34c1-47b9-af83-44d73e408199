# PyStation 登录系统使用指南

## 📋 概述

PyStation 现在配备了增强的登录系统，支持每月动态密码，提供更好的安全性和用户体验。

## 🔐 密码规则

### 默认密码格式
- **管理员账户 (admin)**: `py + 月份 + 年份后两位`
- **普通用户 (user)**: `data + 月份 + 日期`

### 示例
```
2025年8月 (08月):
- admin密码: py0825
- user密码: data0801 (假设是1号)

2025年12月 (12月):
- admin密码: py1225
- user密码: data1201 (假设是1号)
```

## 🛡️ 安全特性

### 1. 登录尝试限制
- 最大失败尝试次数: **3次**
- 超过限制后锁定时间: **5分钟**
- 锁定期间无法输入用户名和密码

### 2. 密码提示
- 登录界面显示密码格式提示
- 帮助用户理解当前密码规则

### 3. 实时密码显示（开发模式）
- 当前版本在登录界面显示实际密码
- **生产环境部署时应移除此功能**

## 🎨 界面特性

### 美观的UI设计
- 现代化的界面风格
- 清晰的错误和成功提示
- 响应式的按钮和输入框

### 用户体验优化
- 支持回车键快速登录
- 智能焦点管理
- 实时状态反馈

## ⚙️ 配置管理

### 配置文件位置
```
configs/login_config.json
```

### 配置文件结构
```json
{
  "users": {
    "admin": {
      "enabled": true,
      "password_pattern": "py{month}{year_last2}",
      "description": "管理员账户"
    }
  },
  "security": {
    "max_attempts": 3,
    "lockout_minutes": 5,
    "password_hint": "密码格式提示"
  }
}
```

### 密码模式变量
- `{month}`: 月份 (01-12)
- `{year_last2}`: 年份后两位 (25 for 2025)
- `{year}`: 完整年份 (2025)
- `{day}`: 日期 (01-31)

## 🔧 管理工具

### 密码管理工具
运行 `python password_tool.py` 可以:
1. 查看当前所有用户的密码
2. 预览未来6个月的密码
3. 查看完整配置信息

### 使用示例
```bash
python password_tool.py
```

## 📝 自定义配置

### 添加新用户
```json
{
  "users": {
    "newuser": {
      "enabled": true,
      "password_pattern": "custom{month}{day}",
      "description": "新用户"
    }
  }
}
```

### 修改安全设置
```json
{
  "security": {
    "max_attempts": 5,        // 增加尝试次数
    "lockout_minutes": 10,    // 延长锁定时间
    "password_hint": "自定义提示"
  }
}
```

## 🚀 部署建议

### 生产环境配置
1. **移除密码显示**: 删除登录界面的当前密码显示
2. **加强密码复杂度**: 使用更复杂的密码模式
3. **定期更换规则**: 每季度更新密码生成规则
4. **日志记录**: 添加登录尝试日志

### 安全最佳实践
1. 定期备份配置文件
2. 限制配置文件访问权限
3. 监控异常登录尝试
4. 定期审查用户账户

## 🔍 故障排除

### 常见问题

**Q: 忘记当前密码怎么办？**
A: 运行 `python password_tool.py` 查看当前密码

**Q: 如何重置失败尝试计数？**
A: 等待锁定时间结束，或重启应用程序

**Q: 如何添加新的密码模式？**
A: 编辑 `configs/login_config.json` 文件中的 `password_pattern`

**Q: 登录界面无法显示？**
A: 检查PyQt5是否正确安装，确保所有依赖库可用

## 📞 技术支持

如有问题，请检查:
1. 配置文件格式是否正确
2. 系统时间是否准确
3. 依赖库是否完整安装
4. 文件权限是否正确
