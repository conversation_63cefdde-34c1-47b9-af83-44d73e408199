"""
SPC统计表格窗口模块
提供SPC统计数据的表格显示和分析功能
"""

# ============================================================================
# 标准库导入
# ============================================================================
import sys

# ============================================================================
# 第三方库导入
# ============================================================================
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem,
    QSizePolicy, QMenu, QAction
)
from PyQt5.QtCore import Qt


def calculate_spc_data(df):
    """计算SPC统计数据"""
    points = df['point'].drop_duplicates().tolist()
    points = sorted(points)
    
    df_spc = pd.DataFrame()
    for point in points:
        df_filter = df[df['point'] == point]
        df_filter = df_filter.sort_values(by=['snplus'])
        
        dv_usl = df_filter['up_tol'].drop_duplicates().values.tolist()[0]
        dv_lsl = df_filter['low_tol'].drop_duplicates().values.tolist()[0]
        
        dv_max = df_filter['deviation'].max()
        dv_min = df_filter['deviation'].min()
        dv_c = (dv_usl + dv_lsl) / 2
        dv_t = dv_usl - dv_lsl
        dv_mean = df_filter['deviation'].mean()
        dv_std = df_filter['deviation'].std()
        dv_ca = (dv_mean - dv_c) / (dv_t / 2)
        dv_cp = dv_t / (6 * dv_std)
        dv_cpk = dv_cp * (1 - abs(dv_ca))
        
        data = {
            'point': [point],
            'max': [round(dv_max, 2)],
            'min': [round(dv_min, 2)],
            'mean': [round(dv_mean, 2)],
            'std': [round(dv_std, 2)],
            'ca': [round(dv_ca, 2)],
            'cp': [round(dv_cp, 2)],
            'cpk': [round(dv_cpk, 2)],
            'nominal_quantity': [len(df_filter['nominal'].drop_duplicates().values.tolist())],
            'up_tol_quantity': [len(df_filter['up_tol'].drop_duplicates().values.tolist())],
            'low_tol_quantity': [len(df_filter['low_tol'].drop_duplicates().values.tolist())]
        }
        df_statistics = pd.DataFrame(data)
        df_spc = pd.concat([df_spc, df_statistics], ignore_index=True)
    
    df_spc = df_spc.sort_values(by='cpk', ascending=False).reset_index(drop=True)
    
    # 创建透视表
    df_deviation = pd.pivot_table(df, values='deviation',
                                  index=['point', 'dimension', 'feature', 'axis', 'nominal', 'up_tol', 'low_tol'],
                                  columns=['sn']).reset_index()
    
    # 合并数据
    df_result = pd.merge(df_spc, df_deviation, on='point')
    return df_result


class GraphWindow(QWidget):
    """详细点图窗口"""
    def __init__(self, df: pd.DataFrame, point: str):
        super().__init__()
        self.ax = None
        self.df = df
        self.point = point
        self.initUI()

    def initUI(self) -> None:
        try:
            fig, ax, line = self.fig_output()
            self.ax = ax
            self.canvas = FigureCanvas(fig)
            from matplotlib.backends.backend_qt import NavigationToolbar2QT as NavigationToolbar
            self.toolbar = NavigationToolbar(self.canvas, self)
            self.canvas.mpl_connect('motion_notify_event', self.on_hover_2)
            layout = QVBoxLayout()
            layout.addWidget(self.toolbar)
            layout.addWidget(self.canvas)
            self.setLayout(layout)
            self.setWindowTitle(f"Graph for {self.point}")
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(None, "错误", f"详细图形窗口初始化失败: {e}")

    def fig_output(self):
        try:
            df_filter = self.df[self.df['point'] == self.point].sort_values(by=['snplus'])
            sn_quantity = len(df_filter['sn'].drop_duplicates().values.tolist())
            fig, ax = plt.subplots()
            fig.set_size_inches(13.66/2, 7.68/2)
            ax.set_xlabel('snplus')
            ax.set_ylabel('deviation')
            ax.tick_params(axis='both', labelsize=6)
            ax.set_xticks(range(0, sn_quantity + 1, 1))
            line, = ax.plot(df_filter['snplus'], df_filter['deviation'], color='blue', linewidth=2, marker='o', markersize=8)
            ax.plot(df_filter['snplus'], df_filter['up_tol'], color='red', linewidth=2, linestyle='--')
            ax.plot(df_filter['snplus'], df_filter['low_tol'], color='red', linewidth=2, linestyle='--')
            # 新增：中间基准线（黑色）
            ax.plot(df_filter['snplus'], (df_filter['up_tol'] + df_filter['low_tol'])/2, color='green', linewidth=2)
            # 新增：80%公差辅助线（橙色虚线）
            ax.plot(df_filter['snplus'], df_filter['up_tol']*0.8, color='orange', linewidth=1, linestyle='--')
            ax.plot(df_filter['snplus'], df_filter['low_tol']*0.8, color='orange', linewidth=1, linestyle='--')
            return fig, ax, line
        except Exception as e:
            raise RuntimeError(f"图形生成失败: {e}")

    def on_hover_2(self, event) -> None:
        """鼠标悬停在折线图上时显示详细数据提示"""
        from PyQt5.QtWidgets import QToolTip
        from PyQt5.QtGui import QCursor
        import numpy as np
        
        if event.inaxes != self.ax:
            QToolTip.hideText()
            return
        
        df_filter = self.df[self.df['point'] == self.point].sort_values(by=['snplus'])
        threshold = 0.05
        
        if self.ax is not None and hasattr(self.ax, 'lines'):
            if len(self.ax.lines) > 0:
                line = self.ax.lines[0]
                x_data, y_data = line.get_data()
                x_event, y_event = event.xdata, event.ydata
                if x_event is not None and y_event is not None:
                    distances = np.sqrt((x_data - x_event) ** 2 + (y_data - y_event) ** 2)
                    min_distance = np.min(distances)
                    if min_distance < threshold:
                        idx = np.argmin(distances)
                        label = '偏差值'
                        y = df_filter['deviation'].iloc[idx]
                        x = df_filter['snplus'].iloc[idx]
                        sn = df_filter['sn'].iloc[idx]
                        QToolTip.showText(
                            QCursor.pos(),
                            f'{label}\nx: {x}\ny: {y:.3f}\nsn: {sn}'
                        )
                        return
        QToolTip.hideText()


class SpcTableWindow(QWidget):
    def __init__(self, df, original_df):
        super().__init__()
        self.df = df
        self.original_df = original_df  # 保存原始数据用于绘图
        self.graph_windows = []
        self.initUI()

    def initUI(self):
        self.setWindowTitle('SPC统计表格')
        self.resize(1000, 600)

        layout = QVBoxLayout()

        # 创建搜索框
        from PyQt5.QtWidgets import QHBoxLayout, QLineEdit, QLabel, QPushButton
        search_layout = QHBoxLayout()
        search_label = QLabel("搜索:")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入搜索关键词...")
        self.search_input.textChanged.connect(self.filter_table)
        clear_button = QPushButton("清除")
        clear_button.clicked.connect(self.clear_search)
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(clear_button)
        search_layout.addStretch()  # 添加弹性空间

        self.table = QTableWidget()
        self.table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self.show_table_menu)

        self.table.setRowCount(self.df.shape[0])
        self.table.setColumnCount(self.df.shape[1])
        self.table.setHorizontalHeaderLabels(self.df.columns)

        # 存储原始数据用于搜索
        self.original_data = []
        for row in range(self.df.shape[0]):
            row_data = []
            for col in range(self.df.shape[1]):
                item_text = str(self.df.iloc[row, col])
                item = QTableWidgetItem(item_text)
                item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                self.table.setItem(row, col, item)
                row_data.append(item_text)
            self.original_data.append(row_data)

        self.table.resizeColumnsToContents()
        self.table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        layout.addLayout(search_layout)
        layout.addWidget(self.table)
        self.setLayout(layout)

    def show_table_menu(self, pos):
        menu = QMenu(self.table)
        copy_action = QAction("复制所选内容", self.table)
        copy_action.triggered.connect(self.copy_selected_content)
        menu.addAction(copy_action)

        # 添加保存到CSV功能
        save_csv_action = QAction("保存表格到CSV", self.table)
        save_csv_action.triggered.connect(self.save_table_to_csv)
        menu.addAction(save_csv_action)
        
        # 支持多选point列右键graph
        selected_items = self.table.selectedItems()
        point_indices = []
        for item in selected_items:
            col = item.column()
            header_item = self.table.horizontalHeaderItem(col)
            if header_item is not None and header_item.text() == 'point':
                point_indices.append(item)
        
        if point_indices:
            graph_action = QAction("graph", self.table)
            def show_all_graphs():
                for item in point_indices:
                    self.show_graph(item.text())
            graph_action.triggered.connect(show_all_graphs)
            menu.addAction(graph_action)
        
        menu.exec_(self.table.mapToGlobal(pos))

    def copy_selected_content(self):
        # 获取选中的项目
        selected_items = self.table.selectedItems()
        if not selected_items:
            return

        # 获取选中项目的行列信息
        selected_ranges = {}
        for item in selected_items:
            row = item.row()
            col = item.column()
            if row not in selected_ranges:
                selected_ranges[row] = []
            selected_ranges[row].append(col)

        # 按行排序
        sorted_rows = sorted(selected_ranges.keys())

        content = ""
        for i, row in enumerate(sorted_rows):
            # 按列排序
            sorted_cols = sorted(selected_ranges[row])
            row_data = []
            for col in sorted_cols:
                item = self.table.item(row, col)
                row_data.append(item.text() if item else "")
            content += "\t".join(row_data)
            if i < len(sorted_rows) - 1:  # 不是最后一行
                content += "\n"

        clipboard = QApplication.clipboard()
        clipboard.setText(content)

    def save_table_to_csv(self):
        """保存表格数据到CSV文件"""
        from PyQt5.QtWidgets import QFileDialog, QMessageBox
        import csv
        import os

        try:
            # 弹出文件保存对话框
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "保存表格到CSV",
                "spc_data.csv",
                "CSV Files (*.csv);;All Files (*)"
            )

            if not file_path:
                return  # 用户取消了保存

            # 确保文件扩展名为.csv
            if not file_path.lower().endswith('.csv'):
                file_path += '.csv'

            # 写入CSV文件
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)

                # 写入列名
                headers = []
                for col in range(self.table.columnCount()):
                    header_item = self.table.horizontalHeaderItem(col)
                    if header_item is not None:
                        headers.append(header_item.text())
                    else:
                        headers.append(f"Column_{col}")
                writer.writerow(headers)

                # 写入数据行
                for row in range(self.table.rowCount()):
                    row_data = []
                    for col in range(self.table.columnCount()):
                        item = self.table.item(row, col)
                        row_data.append(item.text() if item else "")
                    writer.writerow(row_data)

            QMessageBox.information(self, "保存成功", f"表格已成功保存到:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存CSV文件时发生错误:\n{str(e)}")

    def filter_table(self):
        """根据搜索关键词过滤表格行"""
        search_text = self.search_input.text().lower()

        if not search_text:
            # 如果搜索框为空，显示所有行
            for row in range(self.table.rowCount()):
                self.table.setRowHidden(row, False)
            return

        # 遍历所有行，检查是否包含搜索关键词
        for row in range(self.table.rowCount()):
            row_contains_text = False
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                if item and search_text in item.text().lower():
                    row_contains_text = True
                    break

            # 隐藏或显示行
            self.table.setRowHidden(row, not row_contains_text)

    def clear_search(self):
        """清除搜索框并显示所有行"""
        self.search_input.clear()
        for row in range(self.table.rowCount()):
            self.table.setRowHidden(row, False)

    def show_graph(self, point: str) -> None:
        try:
            graph_window = GraphWindow(self.original_df, point)
            graph_window.show()
            self.graph_windows.append(graph_window)
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(None, "错误", f"显示详细图形失败: {e}")


def show_spc_table(df_tmp):
    """显示SPC表格的便捷函数"""
    df_result = calculate_spc_data(df_tmp)
    window = SpcTableWindow(df_result, df_tmp)  # 传入原始数据
    window.showMaximized()
    return window
