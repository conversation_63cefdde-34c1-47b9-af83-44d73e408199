
"""
登录窗口模块
提供用户登录验证功能，支持每月动态密码
"""

# ============================================================================
# 标准库导入
# ============================================================================
import hashlib
import datetime
import json
import os
from pathlib import Path
from typing import TYPE_CHECKING, Dict, Optional

# ============================================================================
# 第三方库导入
# ============================================================================
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QMessageBox, QFrame, QApplication, QProgressBar
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPalette

if TYPE_CHECKING:
    from main import MainWindow


class PasswordManager:
    """密码管理器 - 处理每月动态密码生成和验证"""

    def __init__(self):
        self.config_file = Path("configs") / "login_config.json"
        self.config_file.parent.mkdir(exist_ok=True)
        self.load_config()

    def load_config(self) -> None:
        """加载登录配置"""
        default_config = {
            "users": {
                "admin": {
                    "enabled": True,
                    "password_pattern": "py{month}{year_last2}",
                    "description": "管理员账户"
                }
            },
            "security": {
                "max_attempts": 3,
                "lockout_minutes": 5,
                "password_hint": "格式: py + 月份 + 年份后两位"
            }
        }

        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            except Exception:
                self.config = default_config
                self.save_config()
        else:
            self.config = default_config
            self.save_config()

    def save_config(self) -> None:
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存登录配置失败: {e}")

    def generate_current_password(self, username: str) -> str:
        """生成当前月份的密码"""
        if username not in self.config["users"]:
            return ""

        user_config = self.config["users"][username]
        pattern = user_config.get("password_pattern", "")

        now = datetime.datetime.now()

        # 替换模式中的变量
        password = pattern.format(
            month=f"{now.month:02d}",  # 01-12
            year_last2=f"{now.year % 100:02d}",  # 25 for 2025
            year=str(now.year),  # 2025
            day=f"{now.day:02d}"  # 01-31
        )

        return password

    def verify_password(self, username: str, password: str) -> bool:
        """验证用户名和密码"""
        if username not in self.config["users"]:
            return False

        user_config = self.config["users"][username]
        if not user_config.get("enabled", False):
            return False

        expected_password = self.generate_current_password(username)
        return password == expected_password

    def get_password_hint(self) -> str:
        """获取密码提示"""
        return self.config["security"].get("password_hint", "")

    def get_max_attempts(self) -> int:
        """获取最大尝试次数"""
        return self.config["security"].get("max_attempts", 3)

    def get_lockout_minutes(self) -> int:
        """获取锁定时间（分钟）"""
        return self.config["security"].get("lockout_minutes", 5)


class LoginWindow(QWidget):
    """增强的登录窗口 - 支持每月动态密码"""

    # 定义信号
    login_successful = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.password_manager = PasswordManager()
        self.failed_attempts = 0
        self.lockout_timer = QTimer()
        self.lockout_timer.timeout.connect(self.unlock_login)

        self.setup_ui()
        self.setup_styles()

    def setup_ui(self) -> None:
        """设置用户界面"""
        self.setWindowTitle('PyStation - 用户登录')
        self.setFixedSize(400, 300)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)

        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(30, 30, 30, 30)

        # 标题
        title_label = QLabel('PyStation 数据分析系统')
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        main_layout.addWidget(title_label)

        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(line)

        # 用户名输入
        user_layout = QHBoxLayout()
        user_label = QLabel('用户名:')
        user_label.setFixedWidth(60)
        self.input_user = QLineEdit('admin')
        self.input_user.setPlaceholderText('请输入用户名')
        user_layout.addWidget(user_label)
        user_layout.addWidget(self.input_user)
        main_layout.addLayout(user_layout)

        # 密码输入
        pass_layout = QHBoxLayout()
        pass_label = QLabel('密码:')
        pass_label.setFixedWidth(60)
        self.input_pass = QLineEdit()
        self.input_pass.setEchoMode(QLineEdit.Password)
        self.input_pass.setPlaceholderText('请输入密码')
        self.input_pass.returnPressed.connect(self.check_login)
        pass_layout.addWidget(pass_label)
        pass_layout.addWidget(self.input_pass)
        main_layout.addLayout(pass_layout)

        # 密码提示
        self.hint_label = QLabel(self.password_manager.get_password_hint())
        self.hint_label.setStyleSheet("color: #666; font-size: 11px;")
        self.hint_label.setWordWrap(True)
        main_layout.addWidget(self.hint_label)

        # 当前密码显示（仅用于演示，实际部署时应移除）
        current_password = self.password_manager.generate_current_password('admin')
        self.demo_label = QLabel(f'当前密码: {current_password}')
        self.demo_label.setStyleSheet("color: #0066cc; font-size: 10px; background: #f0f8ff; padding: 5px; border-radius: 3px;")
        main_layout.addWidget(self.demo_label)

        # 登录按钮
        self.login_btn = QPushButton('登录')
        self.login_btn.clicked.connect(self.check_login)
        self.login_btn.setDefault(True)
        main_layout.addWidget(self.login_btn)

        # 状态标签
        self.status_label = QLabel('')
        self.status_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(self.status_label)

        self.setLayout(main_layout)

    def setup_styles(self) -> None:
        """设置样式"""
        self.setStyleSheet("""
            QWidget {
                background-color: #f5f5f5;
                font-family: "Microsoft YaHei", Arial, sans-serif;
            }
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
            }
            QPushButton {
                padding: 10px;
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QLabel {
                color: #333;
            }
        """)

    def check_login(self) -> None:
        """检查登录凭据"""
        username = self.input_user.text().strip()
        password = self.input_pass.text()

        if not username:
            self.show_error("请输入用户名")
            return

        if not password:
            self.show_error("请输入密码")
            return

        # 验证密码
        if self.password_manager.verify_password(username, password):
            self.accept_login()
        else:
            self.handle_failed_login()

    def handle_failed_login(self) -> None:
        """处理登录失败"""
        self.failed_attempts += 1
        max_attempts = self.password_manager.get_max_attempts()

        remaining = max_attempts - self.failed_attempts

        if remaining > 0:
            self.show_error(f"用户名或密码错误！还有 {remaining} 次尝试机会")
            self.input_pass.clear()
            self.input_pass.setFocus()
        else:
            self.lockout_login()

    def lockout_login(self) -> None:
        """锁定登录"""
        lockout_minutes = self.password_manager.get_lockout_minutes()

        self.login_btn.setEnabled(False)
        self.input_user.setEnabled(False)
        self.input_pass.setEnabled(False)

        self.show_error(f"登录失败次数过多，已锁定 {lockout_minutes} 分钟")

        # 启动解锁定时器
        self.lockout_timer.start(lockout_minutes * 60 * 1000)  # 转换为毫秒

    def unlock_login(self) -> None:
        """解锁登录"""
        self.lockout_timer.stop()
        self.failed_attempts = 0

        self.login_btn.setEnabled(True)
        self.input_user.setEnabled(True)
        self.input_pass.setEnabled(True)

        self.show_success("登录已解锁，请重新尝试")
        self.input_pass.clear()
        self.input_user.setFocus()

    def show_error(self, message: str) -> None:
        """显示错误消息"""
        self.status_label.setText(message)
        self.status_label.setStyleSheet("color: #d32f2f; font-weight: bold;")

    def show_success(self, message: str) -> None:
        """显示成功消息"""
        self.status_label.setText(message)
        self.status_label.setStyleSheet("color: #388e3c; font-weight: bold;")

    def accept_login(self) -> None:
        """接受登录并显示主窗口"""
        self.show_success("登录成功！正在启动系统...")

        # 短暂延迟以显示成功消息
        QTimer.singleShot(500, self._launch_main_window)

    def _launch_main_window(self) -> None:
        """启动主窗口"""
        try:
            # 使用延迟导入避免循环依赖
            from main import MainWindow

            self.hide()
            self.main_window = MainWindow()
            self.main_window.show()

            # 发射登录成功信号
            self.login_successful.emit()

        except Exception as e:
            self.show_error(f"启动主窗口失败: {str(e)}")
            self.show()

    def closeEvent(self, event) -> None:
        """处理窗口关闭事件"""
        # 如果用户关闭登录窗口，退出应用程序
        QApplication.quit()


def show_login_window() -> Optional['MainWindow']:
    """显示登录窗口并返回主窗口实例"""
    app = QApplication.instance()
    if app is None:
        app = QApplication([])

    login_window = LoginWindow()
    login_window.show()

    # 运行事件循环直到登录窗口关闭
    app.exec_()

    # 返回主窗口实例（如果登录成功）
    return getattr(login_window, 'main_window', None)
