
"""
登录窗口模块
提供用户登录验证功能
"""

# ============================================================================
# 标准库导入
# ============================================================================
from typing import TYPE_CHECKING

# ============================================================================
# 第三方库导入
# ============================================================================
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QLabel, QLineEdit, QPushButton, QMessageBox
)

if TYPE_CHECKING:
    from main import MainWindow

class LoginWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('登录')
        self.resize(300, 150)
        layout = QVBoxLayout()
        layout.addWidget(QLabel('账号:'))
        self.input_user = QLineEdit('admin')
        layout.addWidget(self.input_user)
        layout.addWidget(QLabel('密码:'))
        self.input_pass = QLineEdit('x1')
        self.input_pass.setEchoMode(QLineEdit.Password)
        layout.addWidget(self.input_pass)
        btn = QPushButton('登录')
        btn.clicked.connect(self.check_login)
        layout.addWidget(btn)
        self.setLayout(layout)

    def check_login(self):
        if self.input_user.text() == 'admin' and self.input_pass.text() == 'x1':
            self.accept_login()
        else:
            QMessageBox.warning(self, '错误', '账号或密码错误')

    def accept_login(self):
        """接受登录并显示主窗口"""
        # 使用延迟导入避免循环依赖
        from main import MainWindow

        self.hide()
        self.main_window = MainWindow()
        self.main_window.show()

