# PyStation 项目依赖

# ============================================================================
# 核心GUI框架
# ============================================================================
PyQt5>=5.15.0

# ============================================================================
# 数据处理和科学计算
# ============================================================================
pandas>=1.3.0
numpy>=1.21.0
scipy>=1.7.0

# ============================================================================
# 可视化和绘图
# ============================================================================
matplotlib>=3.4.0
pyqtgraph>=0.12.0

# ============================================================================
# 文档处理
# ============================================================================
python-docx>=0.8.11

# ============================================================================
# UI样式
# ============================================================================
qdarkstyle>=3.0.0

# ============================================================================
# 开发工具 (可选)
# ============================================================================
# 代码格式化和检查
isort>=5.9.0
flake8>=4.0.0
pylint>=2.11.0

# 测试框架
pytest>=6.2.0
pytest-qt>=4.0.0

# 类型检查
mypy>=0.910
