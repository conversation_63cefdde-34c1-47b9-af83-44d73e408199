# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'Ui_main.ui'
#
# Created by: PyQt5 UI code generator 5.15.9
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets


class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        MainWindow.setObjectName("MainWindow")
        MainWindow.resize(1042, 739)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(MainWindow.sizePolicy().hasHeightForWidth())
        MainWindow.setSizePolicy(sizePolicy)
        self.centralwidget = QtWidgets.QWidget(MainWindow)
        self.centralwidget.setObjectName("centralwidget")
        self.formLayout = QtWidgets.QFormLayout(self.centralwidget)
        self.formLayout.setObjectName("formLayout")
        self.gridLayout = QtWidgets.QGridLayout()
        self.gridLayout.setObjectName("gridLayout")
        self.label_4 = QtWidgets.QLabel(self.centralwidget)
        self.label_4.setObjectName("label_4")
        self.gridLayout.addWidget(self.label_4, 5, 0, 1, 1)
        self.pushButton_serach = QtWidgets.QPushButton(self.centralwidget)
        self.pushButton_serach.setObjectName("pushButton_serach")
        self.gridLayout.addWidget(self.pushButton_serach, 4, 0, 1, 1)
        self.label_3 = QtWidgets.QLabel(self.centralwidget)
        self.label_3.setObjectName("label_3")
        self.gridLayout.addWidget(self.label_3, 8, 1, 1, 1)
        self.treeView = QtWidgets.QTreeView(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.treeView.sizePolicy().hasHeightForWidth())
        self.treeView.setSizePolicy(sizePolicy)
        self.treeView.setMinimumSize(QtCore.QSize(390, 300))
        self.treeView.setMaximumSize(QtCore.QSize(390, 16777215))
        self.treeView.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)
        self.treeView.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.treeView.setHorizontalScrollMode(QtWidgets.QAbstractItemView.ScrollPerPixel)
        self.treeView.setObjectName("treeView")
        self.gridLayout.addWidget(self.treeView, 6, 0, 5, 1)
        self.PlotWidget = PlotWidget(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.PlotWidget.sizePolicy().hasHeightForWidth())
        self.PlotWidget.setSizePolicy(sizePolicy)
        self.PlotWidget.setObjectName("PlotWidget")
        self.gridLayout.addWidget(self.PlotWidget, 1, 1, 6, 1)
        self.textEdit = QtWidgets.QTextEdit(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.textEdit.sizePolicy().hasHeightForWidth())
        self.textEdit.setSizePolicy(sizePolicy)
        self.textEdit.setMinimumSize(QtCore.QSize(626, 150))
        self.textEdit.setMaximumSize(QtCore.QSize(16777215, 150))
        self.textEdit.setObjectName("textEdit")
        self.gridLayout.addWidget(self.textEdit, 10, 1, 1, 1)
        self.label = QtWidgets.QLabel(self.centralwidget)
        self.label.setObjectName("label")
        self.gridLayout.addWidget(self.label, 0, 0, 1, 1)
        self.listWidget_sn = QtWidgets.QListWidget(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.listWidget_sn.sizePolicy().hasHeightForWidth())
        self.listWidget_sn.setSizePolicy(sizePolicy)
        self.listWidget_sn.setMinimumSize(QtCore.QSize(390, 200))
        self.listWidget_sn.setMaximumSize(QtCore.QSize(390, 300))
        self.listWidget_sn.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.listWidget_sn.setObjectName("listWidget_sn")
        self.gridLayout.addWidget(self.listWidget_sn, 3, 0, 1, 1)
        self.label_5 = QtWidgets.QLabel(self.centralwidget)
        self.label_5.setObjectName("label_5")
        self.gridLayout.addWidget(self.label_5, 0, 1, 1, 1)
        self.listWidget_product = QtWidgets.QListWidget(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.listWidget_product.sizePolicy().hasHeightForWidth())
        self.listWidget_product.setSizePolicy(sizePolicy)
        self.listWidget_product.setMinimumSize(QtCore.QSize(390, 50))
        self.listWidget_product.setMaximumSize(QtCore.QSize(390, 50))
        self.listWidget_product.setObjectName("listWidget_product")
        self.gridLayout.addWidget(self.listWidget_product, 1, 0, 1, 1)
        self.label_2 = QtWidgets.QLabel(self.centralwidget)
        self.label_2.setObjectName("label_2")
        self.gridLayout.addWidget(self.label_2, 2, 0, 1, 1)
        self.pushButton_analysis = QtWidgets.QPushButton(self.centralwidget)
        self.pushButton_analysis.setObjectName("pushButton_analysis")
        self.gridLayout.addWidget(self.pushButton_analysis, 11, 0, 1, 1)
        self.widget_plot = QtWidgets.QWidget(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.widget_plot.sizePolicy().hasHeightForWidth())
        self.widget_plot.setSizePolicy(sizePolicy)
        self.widget_plot.setMinimumSize(QtCore.QSize(0, 0))
        self.widget_plot.setObjectName("widget_plot")
        self.gridLayout.addWidget(self.widget_plot, 7, 1, 1, 1)
        self.formLayout.setLayout(0, QtWidgets.QFormLayout.SpanningRole, self.gridLayout)
        MainWindow.setCentralWidget(self.centralwidget)
        self.menubar = QtWidgets.QMenuBar(MainWindow)
        self.menubar.setGeometry(QtCore.QRect(0, 0, 1042, 23))
        self.menubar.setObjectName("menubar")
        self.menuSetting = QtWidgets.QMenu(self.menubar)
        self.menuSetting.setObjectName("menuSetting")
        self.menuReport = QtWidgets.QMenu(self.menubar)
        self.menuReport.setObjectName("menuReport")
        MainWindow.setMenuBar(self.menubar)
        self.statusbar = QtWidgets.QStatusBar(MainWindow)
        self.statusbar.setObjectName("statusbar")
        MainWindow.setStatusBar(self.statusbar)
        self.actionProduct_path = QtWidgets.QAction(MainWindow)
        self.actionProduct_path.setObjectName("actionProduct_path")
        self.actionExport_report = QtWidgets.QAction(MainWindow)
        self.actionExport_report.setObjectName("actionExport_report")
        self.actionProduct_Pareto = QtWidgets.QAction(MainWindow)
        self.actionProduct_Pareto.setObjectName("actionProduct_Pareto")
        self.actionProduct_PPK = QtWidgets.QAction(MainWindow)
        self.actionProduct_PPK.setObjectName("actionProduct_PPK")
        self.menuSetting.addAction(self.actionProduct_path)
        self.menuReport.addAction(self.actionExport_report)
        self.menuReport.addAction(self.actionProduct_Pareto)
        self.menuReport.addAction(self.actionProduct_PPK)
        self.menubar.addAction(self.menuSetting.menuAction())
        self.menubar.addAction(self.menuReport.menuAction())

        self.retranslateUi(MainWindow)
        QtCore.QMetaObject.connectSlotsByName(MainWindow)

    def retranslateUi(self, MainWindow):
        _translate = QtCore.QCoreApplication.translate
        MainWindow.setWindowTitle(_translate("MainWindow", "Data Center"))
        self.label_4.setText(_translate("MainWindow", "Point tree:"))
        self.pushButton_serach.setText(_translate("MainWindow", "Serach"))
        self.label_3.setText(_translate("MainWindow", "Output:"))
        self.label.setText(_translate("MainWindow", "Product："))
        self.label_5.setText(_translate("MainWindow", "Analysis"))
        self.label_2.setText(_translate("MainWindow", "Serial Number:"))
        self.pushButton_analysis.setText(_translate("MainWindow", "Analysis"))
        self.menuSetting.setTitle(_translate("MainWindow", "Setting"))
        self.menuReport.setTitle(_translate("MainWindow", "Report"))
        self.actionProduct_path.setText(_translate("MainWindow", "Product_path"))
        self.actionExport_report.setText(_translate("MainWindow", "Export report\n"
""))
        self.actionProduct_Pareto.setText(_translate("MainWindow", "Product_Pareto"))
        self.actionProduct_PPK.setText(_translate("MainWindow", "Product_PPK"))
from pyqtgraph import PlotWidget
